package com.voghion.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CalculateWholesaleFreightSkuDto implements Serializable {
    private static final long serialVersionUID = 591223985630527436L;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("国家")
    private String country;

}
