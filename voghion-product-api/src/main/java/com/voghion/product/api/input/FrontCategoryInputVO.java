package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/31 17:30
 * @describe
 */
@Data
@ApiModel
public class FrontCategoryInputVO implements Serializable {

    /**
     * 分类id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 操作的前台类目名称
     */
    @ApiModelProperty(value = "app名称 voghion  serein")
    private String appName;


    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    /**
     * 跳转类型 4-类目，13-自定义商品列表
     */
    @ApiModelProperty(value = " 跳转类型 4-类目，13-自定义商品列表")
    private Integer type;

    /**
     * 数据
     */
    @ApiModelProperty(value = "数据")
    private String value;

    /**
     * 父类ID 0表示当前类目为1级类目
     */
    @ApiModelProperty(value = "父类ID 0表示当前类目为1级类目")
    private Long parentId;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别0  1 2 ")
    private Integer level;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    private String icon;

    /**
     * 选择图标
     */
    @ApiModelProperty(value = "选择图标")
    private String selectIcon;

    /**
     * 排序规则，越小越靠前
     */
    @ApiModelProperty(value = "排序规则，越小越靠前")
    private Integer sort;

    /**
     * ids
     */
    @ApiModelProperty(value = "ids，删除字段")
    private List<Long> ids;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * appChannel
     */
    @ApiModelProperty(value = "app渠道")
    private String appChannel;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer gender;

    /**
     * appChannel
     */
    @ApiModelProperty(value = "男权重")
    private String maleWeight;

    /**
     * appChannel
     */
    @ApiModelProperty(value = "女权重")
    private String femaleWeight;

    /**
     * appChannel
     */
    @ApiModelProperty(value = "通用权重")
    private String generalWeight;

    @ApiModelProperty(value = "app系统，ios或者android")
    private String appSystem;

    @ApiModelProperty(value = "app版本")
    private String appVersion;

    @ApiModelProperty(value = "后台类目id")
    private List<Long> categoryIds;

}
