package com.voghion.product.api.service;

import com.colorlight.base.model.Result;

import java.util.List;

/**
 * @description:
 * @date: 2023/1/4 上午11:52
 * @author: jashley
 */
public interface ChanceGoodsRemoteService {

    /**
     * 每日凌晨跑脚本，先判断每个模板生成的商品的SKU(red-s)项是否和模板一致，并打标。
     * 同时计算每个商品的SKU均价(按德国价格计算即可)，打上价格最低是 的标签。方便运营导出后人工额外加流量
     */
    Result<Boolean> checkChanceGoodsIsSkuConsistentAndIsMinSkuAvgPrice();

    /**
     * 次日0点起脚本，检查所有自动关闭的模板里的商品在架状态,和是否和模板一致，如果<5个和模板一致的且在架的商品，则把模板继续放开给商家复制，差几个可报名几个，报满后再继续自动关闭
     */
    Result<Boolean> checkChanceGoodsTemplateForOpen();

    /**
     * 根据goodsId查询所属同一机会商品模板且价格更低的goodsId列表
     */
    Result<List<Long>> queryGoodsIdsBySameTemplate(Long goodsId);
}
