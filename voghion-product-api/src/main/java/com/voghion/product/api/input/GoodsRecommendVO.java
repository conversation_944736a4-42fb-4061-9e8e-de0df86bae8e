package com.voghion.product.api.input;

import com.colorlight.base.model.PageView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "推荐商品查询入参")
public class GoodsRecommendVO extends PageView {

    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Id")
    private Long goodsId;


    /**
     * 类目Id
     */
    @ApiModelProperty(value = "类目Id")
   private Long categoryId;

    /**
     * 商品Id列表
     */
    @ApiModelProperty(value = "商品Id列表")
   private List<Long> goodsIds;


    /**
     * 商品类目Id列表
     */
    @ApiModelProperty(value = "商品类目Id列表")
    private List<Long> categoryIds;


    /**
     * 虚拟列表id
     */
    @ApiModelProperty(value = "虚拟列表id")
    private Long customId;

}
