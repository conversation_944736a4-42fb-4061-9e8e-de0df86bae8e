package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class QueryAllSkuPriceByGradientCondition implements Serializable {
    private static final long serialVersionUID = -8677443034822255134L;

    @ApiModelProperty("国家")
    private String country;

    private List<SkuInfo> skuInfos;

    @Data
    @ApiModel
    public static class SkuInfo implements Serializable {
        private static final long serialVersionUID = -3485339713361170868L;

        private Long goodsId;

        private Long skuId;

        @ApiModelProperty("数量")
        private Integer num;
    }
}
