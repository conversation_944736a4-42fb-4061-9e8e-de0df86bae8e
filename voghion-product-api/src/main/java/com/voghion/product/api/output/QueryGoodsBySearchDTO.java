package com.voghion.product.api.output;


import com.colorlight.base.model.PageView;

import java.math.BigDecimal;
import java.util.List;

public class QueryGoodsBySearchDTO extends PageView {

    /**
     * 最低价
     */
    private BigDecimal startPrice;


    /**
     * 搜索内容
     */
    private String content;

    /**
     * 是否过滤无效的数据
     */
    private Boolean isVaild;

    /**
     * 最高价
     */

    private BigDecimal endPrice;

    /**
     * 排序类型（1，价格由低到高，2价格由高到底，3，销量由低到高排序，4销量由高到低排序,
     * 5 新款排序,6评分由高到低, 7热度由高到底--暂时与默认相同，8 默认排序，以原始销量进行排序，由高到低）
     */
    private Integer orderBy;

    /**
     * 前台类目Id
     */
    private Long categoryId;

    /**
     * 屏蔽的类目信息
     */
    private List<Long> invalidCategory;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getInvalidCategory() {
        return invalidCategory;
    }

    public void setInvalidCategory(List<Long> invalidCategory) {
        this.invalidCategory = invalidCategory;
    }

    public BigDecimal getStartPrice() {
        return startPrice;
    }

    public void setStartPrice(BigDecimal startPrice) {
        this.startPrice = startPrice;
    }

    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public void setEndPrice(BigDecimal endPrice) {
        this.endPrice = endPrice;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public Boolean getVaild() {
        return isVaild;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setVaild(Boolean vaild) {
        isVaild = vaild;
    }

}
