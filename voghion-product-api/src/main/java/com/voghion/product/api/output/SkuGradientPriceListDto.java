package com.voghion.product.api.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class SkuGradientPriceListDto implements Serializable {
    private static final long serialVersionUID = -873143393711959990L;

    private Long goodsId;

    private Long skuId;

    @ApiModelProperty("当前梯度单价")
    private BigDecimal currentPrice;

    @ApiModelProperty("第一档单价")
    private BigDecimal commonPrice;

    @ApiModelProperty("sku梯度价格")
    private List<SkuGradientPriceDto.GradientPriceDetail> priceMap;
}
