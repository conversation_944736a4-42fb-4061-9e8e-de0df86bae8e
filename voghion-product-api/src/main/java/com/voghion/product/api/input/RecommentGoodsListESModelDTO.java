package com.voghion.product.api.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
public class RecommentGoodsListESModelDTO implements Serializable {


    //国家二字码 All为所有
    private String countryName;

    //后台类目id
    private List<Long> categoryId;

    //商品id
    private List<Long> goodsId;

    //性别
    private Long gender;

    private String index;


    private Integer pageSize = 20;


    private Integer pageNow = 1;

    //  @NotNull(message = "排序不能为空")
    private Integer orderBy = 8;


    private String gcrIndex;


}
