package com.voghion.product.api.output.es;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @date: 2022/6/30 下午3:44
 * @author: jashley
 */
@Data
public class KeywordEsVo implements Serializable {
    private static final long serialVersionUID = -1412908407113752034L;
    private Long id;
    /**
     * 词组
     */
    private String word;
    /**
     * 近义词专用
     */
    private String synonym;
    /**
     * 类型 0关键词 1同义词 2近义词
     */
    private Integer type;

    private Date createTime;
    private Date updateTime;
    private Integer isDel;

    /**
     * 权重
     */
    private String score;
}
