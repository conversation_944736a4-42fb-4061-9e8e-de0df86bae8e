package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
public class GoodsESModelVo implements Serializable {
    private static final long serialVersionUID = 2392826608634860102L;
    private Long id;

    //商品全局编号
    private String itemNumber;

    //商品名称
    private String name;

    //原始商品名称
    private String orginalName;

    //商品类型 1表示单品 2表示组合商品
    private Integer type;

    //是否对应多个品牌 0表示false 1表示是true
    private String isMultiBrand;

    //商品编码
    private String code;

    //区域code,商品上架所属区域,暂时不做处理
    private String areaCode;

    //是否上架 0-下架  1-上架
    private String isShow;

    //创建时间
    private LocalDateTime createTime;

    //状态 1表示正常 99表示禁用
    private Integer status;

    //是否删除 0表示未删除 1表示已删除
    private Integer isDel;

    //最低销售价格
    private BigDecimal minPrice;

    //最低展示价格
    private BigDecimal minMarketPrice;

    //最高销售价格
    private BigDecimal maxPrice;

    //最高展示价格
    private BigDecimal maxMarketPrice;

    //商品主图
    private String mainImage;

    //商品原始名称
    private String originalName;

    //是否已翻译，0:未翻译，1:已翻译
    private Integer translated;

    //冗余类目Id,一个商品只对应一个后台类目Id
    private Long categoryId;

    //冗余product_id
    private Long productId;

    //最低销售价格
    private BigDecimal orginalMinPrice;

    //最低展示价格
    private BigDecimal orginalMinMarketPrice;

    //最高销售价格
    private BigDecimal orginalMaxPrice;

    //最高展示价格
    private BigDecimal orginalMaxMarketPrice;

    //物流标签id
    private Long labelId;

    //物流名称
    private String labelName;

    //运费模板
    private Long freightTemplateId;

    //是否被修复
    private Integer isFix;

    //是否更新0没有 1 有更新
    private Integer updateType;

    //标签 多个;隔开
    private String tag;

    //修改时间
    private Date updateTime;

    //销售量
    private Long sales;

    //渠道标识，暂未定值
    private Integer channel;

    //app渠道 1voghion
    private Integer appChannel;

    //父id
    private Long parentId;

    //国家
    private String country;

    //原始销量
    private Long orginalSales;

    //商品评分
    private Long score;

    //商品权重
    private Long sortValue;


    //店铺id
    private Long shopId;

    //店铺名称（冗余字段展示用）
    private String shopName;


    private String weight;


    private String packageSzie;

    private String principal;


    /**
     * sku列表
     */
    private List<GoodsItemESModel> itemList;

    /**
     * 国家运费
     */
    private List<GoodsFreightEsModel> countryDelivery;

    /**
     * 属性值名称的聚合
     */
    private String propertyValues;


    private List<String> likeName;
    /**
     * 最低价
     */
    private Double startMinPrice;

    /**
     * 最高价
     */
    private Double endMinPrice;


    private String orderBy;


    private String sortKey;

    private BigDecimal countryPrice;


    /**
     * 属性列表
     */
    private List<PropertyESModel> propertyList;

    private Float matchScore;

    private BigDecimal gcr;

    /**
     * 尺码表模板id
     */
    private Long sizeChartTemplateId;

    /**
     * 商品拓展配置(标签)
     */
    private GoodsExtConfigModel goodsExtConfigModel;

    private ListGoodsCommentModel listGoodsCommentModel;

    /**
     * 商品扩展信息
     */
    private GoodsExtDetailModel goodsExtDetailModel;
}
