//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import com.colorlight.base.utils.BeanCopy;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class GoodsEsVo implements Serializable {
    /**
     * 商品id
     */
    private String goodsId;
    /**
     * 商家skuId
     */
    private String originalSkuId;
    /**
     * 规格(SKU名称)
     */
    private String skuName;
    /**
     * 上下架
     */
    private Integer isShow;
    /**
     * 修改账号
     */
    private String operator;
    /**
     * 修改时间
     */
    private String updateTime;
    private Integer isDel;

    private List<GoodsOpt> newGoods;
    private List<GoodsOpt> oldGoods;


    public GoodsEsVo(GoodsEsVo ex, GoodsOpt oldGoods, GoodsOpt newGoods) {
        BeanCopy.copyObjValue(ex, this);
        this.oldGoods = Collections.singletonList(new GoodsOpt(oldGoods));
        this.newGoods = Collections.singletonList(new GoodsOpt(newGoods));
        this.updateTime = newGoods.getUpdateTime();
    }

    public Long getSkuId() {
        if (CollectionUtils.isNotEmpty(newGoods)) {
            return newGoods.get(0).getSkuId();
        }
        return null;
    }


    @Data
    public static class GoodsOpt implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        /**
         * 默认运费
         */
        private BigDecimal freight;
        /**
         * 价格
         */
        private BigDecimal price;
        private Long skuId;
        private String skuName;
        /**
         * 库存
         */
        private Integer stock;
        private String updateTime;
        //    private Integer status;


        public GoodsOpt() {
        }

        public GoodsOpt(GoodsOpt ex) {
            BeanCopy.copyObjValue(ex, this);
        }
    }


}
