package com.voghion.product.api.enums;

import lombok.Getter;

/**
 * GoodsTagEnum
 *
 * <AUTHOR>
 * @date 2022/12/17
 */
@Getter
public enum PageTagEnum {
    /**
     * 1列表标签 2商详 3购物车 4checkout 5订单
     */
    LIST_TAG(1, "列表"),
    DETAIL_TAG(2, "商详"),
    CART_TAG(3, "购物车"),
    CHECKOUT_TAG(4, "checkout"),
    ORDER_TAG(5, "订单"),
    ;

    private final Integer type;
    private final String desc;

    PageTagEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PageTagEnum getEnumByType(Integer type) {
        for (PageTagEnum p : PageTagEnum.values()) {
            if (p.getType().equals(type)) {
                return p;
            }
        }
        return null;
    }
}
