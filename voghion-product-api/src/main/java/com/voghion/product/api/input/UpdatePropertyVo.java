package com.voghion.product.api.input;

import com.voghion.product.model.dto.GoodsItemDTO;
import com.voghion.product.model.dto.PropertyDTO;
import com.voghion.product.model.vo.property.GoodsPropertyVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class UpdatePropertyVo implements Serializable {
    private static final long serialVersionUID = -8848237628195533446L;

    @ApiModelProperty(value = "商品id", required = true)
    private Long goodsId;

    @ApiModelProperty(value = "规格信息",required = true)
    private List<PropertyDTO> properties;

    @ApiModelProperty(value = "sku信息",required = true)
    private List<GoodsItemDTO> skuInfo;

    private Long shopId;

    private Map<String, GoodsItemDTO> skuMap;
}
