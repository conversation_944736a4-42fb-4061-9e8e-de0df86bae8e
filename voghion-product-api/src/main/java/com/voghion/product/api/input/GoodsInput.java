package com.voghion.product.api.input;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品查询对象
 *
 * <AUTHOR>
 */
@Data
public class GoodsInput implements Serializable {

    private static final long serialVersionUID = 8937400883765295351L;
    /**
     * 商品名称
     */
    private Long skuId;

    /**
     * 商品ID列表
     */
    private Long goodsId;

    private List<Long> goodsIds;

    private String countryName;

    /**
     * 每页显示几条记录
     */
    private Integer pageSize = 20;


    private Integer pageNow = 1;

    private Integer testQueryType;

    /**
     * 商品id集合字符串
     */
    private String goodsIdListStr;


    @ApiModelProperty("包含的活动标签")
    private List<Long> includeTagList;

    @ApiModelProperty("包含的自定义标签")
    private List<Long> includeCustomTagList;

    @ApiModelProperty("去除的活动标签")
    private List<Long> excludeTagList;

    @ApiModelProperty("去除的自定义标签")
    private List<Long> excludeCustomTagList;

    @ApiModelProperty("国家")
    private List<String> countryList;

}
