package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
@Data
public class CustomListCategoryDTO implements Serializable {


    private Long id;

    private Long customId;

    private Long frontCategoryId;

    /**
     * 前台类目名称
     */
    private String frontCategoryName;

    /**
     * 排序字段
     */
    private Integer showArea;

    private LocalDateTime createTime;

    private String country;

    private Integer appChannel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long getCustomId() {
        return customId;
    }

    public void setCustomId(Long customId) {
        this.customId = customId;
    }
    public Long getFrontCategoryId() {
        return frontCategoryId;
    }

    public void setFrontCategoryId(Long frontCategoryId) {
        this.frontCategoryId = frontCategoryId;
    }
    public String getFrontCategoryName() {
        return frontCategoryName;
    }

    public void setFrontCategoryName(String frontCategoryName) {
        this.frontCategoryName = frontCategoryName;
    }
    public Integer getShowArea() {
        return showArea;
    }

    public void setShowArea(Integer showArea) {
        this.showArea = showArea;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }


    @Override
    public String toString() {
        return "CustomListCategory{" +
                "id=" + id +
                ", customId=" + customId +
                ", frontCategoryId=" + frontCategoryId +
                ", frontCategoryName=" + frontCategoryName +
                ", showArea=" + showArea +
                ", createTime=" + createTime +
                ", country=" + country +
                ", appChannel=" + appChannel +
                "}";
    }
}
