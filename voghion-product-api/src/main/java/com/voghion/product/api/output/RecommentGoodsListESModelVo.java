package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
@Data
public class RecommentGoodsListESModelVo implements Serializable {

    //商品id
    private Long id;

    //创建时间
    private LocalDateTime createTime;

    //国家二字码 All为所有
    private String countryName;

    //后台类目id
    private Long categoryId;

    //类目等级
    private Long level;

    //排分
    private String score;

    //性别
    private Long gender;

    //排序字段 越小越靠前
    private Long rn;
    /**
     * 最低价
     */
    private Double startMinPrice;

    /**
     * 最高价
     */
    private  Double endMinPrice;


    private String orderBy;


    private String   sortKey;


    private Integer pageSize;


    private Integer pageNow;



}
