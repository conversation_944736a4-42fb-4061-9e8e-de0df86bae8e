package com.voghion.product.api.service.inner.dto;//package com.voghion.product.inner.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsFreightLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * skuid
     */
    private Long skuId;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 国家
     */
    private String code;


    /**
     * 当前运费
     */
    private BigDecimal currentFreight;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 是否删除 0表示未删除 1表示已删除
     */
    private Integer isDel;

    private int opt;


}
