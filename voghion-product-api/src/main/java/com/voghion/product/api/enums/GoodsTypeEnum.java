package com.voghion.product.api.enums;

import com.voghion.product.utils.MathUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum GoodsTypeEnum {
    SINGLE(1, "单品"),
    COMBINED(2, "组合商品"),
    WHOLESALE(3, "批发商品"),
    ;


    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    GoodsTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getMsgByCode(Integer code) {
        for (GoodsTypeEnum enums : GoodsTypeEnum.values()) {
            if (enums.code.equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }


    public static String getMsg(Integer code) {
        List<Integer> codes = MathUtils.splitBinary(code);
        return codes.stream()
                .map(GoodsTypeEnum::getMsgByCode)
                .collect(Collectors.joining("、"));
    }

    public static List<Integer> listAllTypeCodes() {
        return Arrays.stream(GoodsTypeEnum.values()).map(GoodsTypeEnum::getCode).collect(Collectors.toList());
    }

    public static String listAllTypeNameByCodes(Set<Integer> types) {
        if (CollectionUtils.isEmpty(types)) {
            return "";
        }
        return types.stream().map(type -> {
            for (GoodsTypeEnum value : GoodsTypeEnum.values()) {
                if (value.getCode().equals(type)) {
                    return value.getDesc();
                }

            }
            return "None";
        }).collect(Collectors.joining("、"));
    }
}
