package com.voghion.product.api.enums;



/**
 * 商品的MQ协定枚举
 */
public enum GoodsMqEnums{


    GOODS_IMPORT_PLUGIN("GOODS_IMPORT_PLUGIN", "GOODS_PLUGUN", "通过插件导入商品"),

    GOODS_UPDATE_PLUGIN("GOODS_UPDATE_PLUGIN", "GOODS_PLUGUN", "通过插件更新商品"),


    ;


    /**
     * 结果码
     */
    private final String topic;
    /**
     * 返回消息
     */
    private final String group;
    /**
     * 描述
     */
    private final String desc;

     GoodsMqEnums(String topic, String group, String desc) {
        this.topic = topic;
        this.group = group;
        this.desc = desc;
    }



    public String getGroup() {
        return group;
    }

    public String getDesc() {
        return desc;
    }

    public String getTopic() {
        return topic;
    }

    public String getName() {
        return name();
    }

    public static GoodsMqEnums getEnumByCode(String topic) {
        for(GoodsMqEnums p : GoodsMqEnums.values()) {
            if(p.getTopic().equalsIgnoreCase(topic)) {
                return p;
            }
        }
        return null;
    }
    

    @Override
    public String toString() {
        return String.valueOf ( this.topic);
    }

}
