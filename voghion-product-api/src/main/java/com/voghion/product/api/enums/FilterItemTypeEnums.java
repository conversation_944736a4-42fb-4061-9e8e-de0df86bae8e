package com.voghion.product.api.enums;

import lombok.Getter;

@Getter
public enum FilterItemTypeEnums {

    CATEGORY_TYPE(1, "类目","Category"),
    ACTIVITY_TYPE(2, "活动","Campaign"),
    PROPERTY_TYPE(3, "属性",""),
    ;

    private  Integer code;
    private  String desc;
    private String value;

    FilterItemTypeEnums(Integer code, String desc,String value) {
        this.code = code;
        this.desc = desc;
        this.value=value;
    }
}
