//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import com.voghion.product.inner.dto.GoodsFreightLogDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class GoodsOperateFreightLogVo implements Serializable {
    private static final long serialVersionUID = -4689043259529845368L;
    private Long goodsId;
    private Long skuId;

    private UpdateInfo oldFreightInfo;
    private UpdateInfo newFreightInfo;

    public GoodsOperateFreightLogVo(GoodsFreightLogDto ex) {
        this.goodsId = ex.getGoodsId();
        this.skuId = ex.getSkuId();
        this.oldFreightInfo = new UpdateInfo();
        this.newFreightInfo = new UpdateInfo(ex);
    }

    public GoodsOperateFreightLogVo(GoodsFreightLogDto oldEx, GoodsFreightLogDto newEx) {
        this.goodsId = oldEx.getGoodsId();
        this.skuId = oldEx.getSkuId();
        this.oldFreightInfo = new UpdateInfo(oldEx);
        this.newFreightInfo = new UpdateInfo(newEx);
    }


    @Data
    @NoArgsConstructor
    public static class UpdateInfo implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        private Integer opt;
        private String code;
        /**
         * 默认运费
         */
        private BigDecimal currentFreight;
        /**
         * 价格
         */
        private BigDecimal price;
        private Date createTime;
        private Date updateTime;


        public UpdateInfo(GoodsFreightLogDto ex) {
            this.opt = ex.getOpt();
            this.code = ex.getCode();
            this.currentFreight = ex.getCurrentFreight();
            this.price = ex.getPrice();
            this.createTime = ex.getCreateTime();
            this.updateTime = ex.getUpdateTime();
        }
    }
}
