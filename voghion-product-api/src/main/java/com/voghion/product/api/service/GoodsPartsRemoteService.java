package com.voghion.product.api.service;


import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GarageInfoDTO;
import com.voghion.product.api.dto.GoodsPartsDTO;
import com.voghion.product.api.dto.MyGarageDTO;
import com.voghion.product.api.input.GoodsPartsInput;
import com.voghion.product.api.input.MyGarageInput;

import java.util.List;

public interface GoodsPartsRemoteService {

    Result<List<GoodsPartsDTO>> listGoodsParts(GoodsPartsInput input);

    Result<Boolean> addMyGarage(MyGarageDTO myGarageDTO);

    Result<List<GarageInfoDTO>> queryMyGarage(MyGarageInput input);

    Result<Boolean> deleteMyGarage(MyGarageInput input);

    Result<Boolean> setMyFavourite(MyGarageInput input);

    Result<List<String>> getSelectList(MyGarageInput input);

    Result<List<GarageInfoDTO>> queryPartsList(MyGarageInput input);
}
