package com.voghion.product.api.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息+商品SKU详情
 *
 * <AUTHOR> chen<PERSON>uan
 * @date 2021/06/18 21:57
 **/
@Data
@ApiModel("商品信息+商品SKU详情")
public class GoodInfoOutput implements Serializable {

    private static final long serialVersionUID = -931279226744075776L;
    @ApiModelProperty(value = "分类Id")
    private Long categoryId;

    @ApiModelProperty(value = "商品Id")
    private Long goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品状态")
    private Integer goodsStatus;

    @ApiModelProperty(value = "商品主图")
    private String  mainImage;

    @ApiModelProperty(value = "SKU详情")
    private List<GoodsItemDTO> goodsItemDTOList;

}
