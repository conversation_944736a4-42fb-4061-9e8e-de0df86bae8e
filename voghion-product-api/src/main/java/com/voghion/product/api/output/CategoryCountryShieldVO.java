package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2021/5/28 16:59
 * @describe
 */
@Data
public class CategoryCountryShieldVO implements Serializable {


    private Long id;

    /**
     * 类目国家配置id
     */
    private Long categoryCountryConfigId;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 后台类目Id
     */
    private Long categoryId;

    /**
     * 后台类目名称
     */
    private String categoryName;

    /**
     *  0表示未删除 1表示已删除  2 使用中
     */
    private Integer status;

    /**
     * 0 所有平台  1 android  2 ios   3 pc 4 h5
     */
    private Integer platform;

    private Long parentId;

}
