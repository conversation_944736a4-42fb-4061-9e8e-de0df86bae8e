package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.model.dto.ListingReplaceGoodsDTO;
import com.voghion.product.model.dto.ReplaceGoodsInfoDTO;
import com.voghion.product.model.dto.SyncListingGoodsSortDTO;

public interface ListingRemoteService {

    Result<Void> syncListingGoodsSort();

    Result<Void> syncListingGoodsSort(SyncListingGoodsSortDTO dto);

    Result<Void> calculateListingGoodsHot();

    Result<Void> refreshNegativeListingGoodsVat();

    Result<Void> refreshListingGoodsName();

    Result<ListingReplaceGoodsDTO> queryOrderReplaceGoods(ReplaceGoodsInfoDTO replaceGoodsInfoDTO);

    Result<Void> refreshListingType();

    Result<Void> listingReductionPushNoticeJob();
}
