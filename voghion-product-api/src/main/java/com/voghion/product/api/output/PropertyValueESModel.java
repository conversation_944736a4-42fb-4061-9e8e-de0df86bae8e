package com.voghion.product.api.output;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 属性值表
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
public class PropertyValueESModel  implements Serializable {


    private Long id;

    //属性值
    private String value;

    private Long propertyId;

    //类目ID，冗余字段
    private Long categoryId;

    //排序
    private Integer sort;

    //创建时间
    private LocalDateTime createTime;

    //状态 1表示正常 99表示禁用
    private Integer status;

    //原始属性值
    private String originalValue;

    //是否已翻译，0:未翻译，1:已翻译
    private Integer translated;

    //图片链接
    private String imgUrl;

    //大图链接
    private String mainUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }
    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getOriginalValue() {
        return originalValue;
    }

    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    public Integer getTranslated() {
        return translated;
    }

    public void setTranslated(Integer translated) {
        this.translated = translated;
    }
    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
    public String getMainUrl() {
        return mainUrl;
    }

    public void setMainUrl(String mainUrl) {
        this.mainUrl = mainUrl;
    }


}
