//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output.es;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class GoodsEsItemVo implements Serializable {
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 操作类型 0新增 1修改 2删除
     */
    private Integer opt;
    /**
     * 默认运费
     */
    private BigDecimal defaultDelivery;
    /**
     * 价格
     */
    private BigDecimal orginalPrice;
    /**
     * 库存
     */
    private Integer stock;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;


}
