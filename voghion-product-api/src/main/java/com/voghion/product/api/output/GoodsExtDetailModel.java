package com.voghion.product.api.output;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@Data
public class GoodsExtDetailModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 重量字段
     */
    private String weight;

    /**
     * 体积
     */
    private String packageSize;

    /**
     * 商品规格描述
     */
    private String specs;

    private Integer channel;

    private String itemCode;

    private String goodsUrl;

    @ApiModelProperty("商品采购链接")
    private String costUrl;

    private String shopName;

    private String shopUrl;

    private LocalDateTime createTime;

    /**
     * 总评论数（3-50条之间随机生成）
     */
    private Integer commentNumber;

    /**
     * 论分
     */
    private Double score;

    /**
     * 生产周期(单位天)
     */
    private Integer leadTime;

    /**
     * 店铺id 0为KFBUY店铺
     */
    private Long storeId;

    /**
     * 处理时间，格式 1-3
     */
    private String dealTime;

    /**
     * 配送时间，格式 4-3
     */
    private String deliveryTime;

    /**
     * 处理时间，格式 1-3
     */
    private String deliveryCountry;

    /**
     * 原始店铺名称
     */
    private String originalShopName;

    /**
     * 原始商品规格描述
     */
    private String originalSpecs;

    /**
     * 是否已翻译，0:未翻译，1:已翻译
     */
    private Integer translated;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 所属小二
     */
    private String principal;
    /**
     * 采购供应商
     */
    private String procureSupplier;

}
