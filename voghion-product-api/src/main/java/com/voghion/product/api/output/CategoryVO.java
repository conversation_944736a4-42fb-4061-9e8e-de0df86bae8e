package com.voghion.product.api.output;

import com.colorlight.base.model.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/28 16:00
 * @describe
 */
@Data
@ApiModel
public class CategoryVO extends BaseVO {


    @ApiModelProperty(value = "类目id")
    private Long id;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String name;

    /**
     * 类目原始名称
     */
    private String orginalName;

    /**
     * 父节点
     */
    private Long parentId;

    /**
     * 类目等级
     */
    private Integer level;

    /**
     * 所有父节点
     */
    private String pids;

    /**
     * 类目图标
     */
    private String imgUrl;

    /**
     * 是否是叶子 1是 2否
     */
    private Integer isLeaf;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 子类目
     */
    @ApiModelProperty(value = "子类目")
    private List<CategoryVO> childCategory;

    private CategoryVO firstLevelCategory;

    /**
     * 创建时间
     */
    private Date createTime;

    private Integer type;
}
