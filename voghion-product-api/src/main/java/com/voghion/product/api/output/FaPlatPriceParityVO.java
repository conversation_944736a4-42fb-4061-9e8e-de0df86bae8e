package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Data
public class FaPlatPriceParityVO implements Serializable  {


    private Integer id;

    /**
     * 比价商品id
     */
    private Integer goodsId;

    /**
     * 市场/平台名称
     */
    private String platform;

    /**
     * 平台最低价格
     */
    private BigDecimal money;


    /**
     * 平台最高价格
     */
    private BigDecimal maxMoney;

    /**
     * 商品链接
     */
    private String goodsUrl;

    /**
     * 平台图片
     */
    private String platImg;

    /**
     * 权重
     */
    private Integer weigh;

    /**
     * 是否打开0否1是
     */
    private Integer isOn;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
