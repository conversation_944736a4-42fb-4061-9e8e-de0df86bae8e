//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class GoodsESModel implements Serializable {
    private static final long serialVersionUID = 1241116049092129754L;
    private Long id;
    private String itemNumber;
    private String name;
    private String orginalName;
    private Integer type;
    private String isMultiBrand;
    private String code;
    private String areaCode;
    private String isShow;
    private LocalDateTime createTime;
    private Integer status;
    private Integer isDel;
    private BigDecimal minPrice;
    private BigDecimal minMarketPrice;
    private BigDecimal maxPrice;
    private BigDecimal maxMarketPrice;
    private String mainImage;
    private String originalName;
    private Integer translated;
    private Long categoryId;
    private Long productId;
    private BigDecimal orginalMinPrice;
    private BigDecimal orginalMinMarketPrice;
    private BigDecimal orginalMaxPrice;
    private BigDecimal orginalMaxMarketPrice;
    private Long labelId;
    private String labelName;
    private Long freightTemplateId;
    private Integer isFix;
    private Integer updateType;
    private String tag;
    private Date updateTime;
    private Long sales;
    private Integer channel;
    private Integer appChannel;
    private Long parentId;
    private String country;
    private Long orginalSales;
    private Long score;
    private Long sortValue;
    private Long shopId;
    private String shopName;
    private String weight;
    private String packageSzie;
    private List<PropertyESModel> propertyList;
    private List<GoodsItemESModel> itemList;
    private String propertyValues;
    private List<PropertyValueESModel> propertyValueList;
    private List<GoodsImageESModel> goodsImageList;
    private List<GoodsExtDetailImgESModel> goodsExtDetailImgList;
    private List<String> likeName;
    private Double startMinPrice;
    private Double endMinPrice;
    private String orderBy;
    private String sortKey;
    private Integer pageSize=10;
    private Integer pageNow=1;

    /**
     * 尺码表模板id
     */
    private Long sizeChartTemplateId;


    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof GoodsESModel)) {
            return false;
        } else {
            GoodsESModel other = (GoodsESModel)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label695: {
                    Object this$id = this.getId();
                    Object other$id = other.getId();
                    if (this$id == null) {
                        if (other$id == null) {
                            break label695;
                        }
                    } else if (this$id.equals(other$id)) {
                        break label695;
                    }

                    return false;
                }

                Object this$itemNumber = this.getItemNumber();
                Object other$itemNumber = other.getItemNumber();
                if (this$itemNumber == null) {
                    if (other$itemNumber != null) {
                        return false;
                    }
                } else if (!this$itemNumber.equals(other$itemNumber)) {
                    return false;
                }

                label681: {
                    Object this$name = this.getName();
                    Object other$name = other.getName();
                    if (this$name == null) {
                        if (other$name == null) {
                            break label681;
                        }
                    } else if (this$name.equals(other$name)) {
                        break label681;
                    }

                    return false;
                }

                Object this$orginalName = this.getOrginalName();
                Object other$orginalName = other.getOrginalName();
                if (this$orginalName == null) {
                    if (other$orginalName != null) {
                        return false;
                    }
                } else if (!this$orginalName.equals(other$orginalName)) {
                    return false;
                }

                label667: {
                    Object this$type = this.getType();
                    Object other$type = other.getType();
                    if (this$type == null) {
                        if (other$type == null) {
                            break label667;
                        }
                    } else if (this$type.equals(other$type)) {
                        break label667;
                    }

                    return false;
                }

                Object this$isMultiBrand = this.getIsMultiBrand();
                Object other$isMultiBrand = other.getIsMultiBrand();
                if (this$isMultiBrand == null) {
                    if (other$isMultiBrand != null) {
                        return false;
                    }
                } else if (!this$isMultiBrand.equals(other$isMultiBrand)) {
                    return false;
                }

                label653: {
                    Object this$code = this.getCode();
                    Object other$code = other.getCode();
                    if (this$code == null) {
                        if (other$code == null) {
                            break label653;
                        }
                    } else if (this$code.equals(other$code)) {
                        break label653;
                    }

                    return false;
                }

                label646: {
                    Object this$areaCode = this.getAreaCode();
                    Object other$areaCode = other.getAreaCode();
                    if (this$areaCode == null) {
                        if (other$areaCode == null) {
                            break label646;
                        }
                    } else if (this$areaCode.equals(other$areaCode)) {
                        break label646;
                    }

                    return false;
                }

                Object this$isShow = this.getIsShow();
                Object other$isShow = other.getIsShow();
                if (this$isShow == null) {
                    if (other$isShow != null) {
                        return false;
                    }
                } else if (!this$isShow.equals(other$isShow)) {
                    return false;
                }

                label632: {
                    Object this$createTime = this.getCreateTime();
                    Object other$createTime = other.getCreateTime();
                    if (this$createTime == null) {
                        if (other$createTime == null) {
                            break label632;
                        }
                    } else if (this$createTime.equals(other$createTime)) {
                        break label632;
                    }

                    return false;
                }

                label625: {
                    Object this$status = this.getStatus();
                    Object other$status = other.getStatus();
                    if (this$status == null) {
                        if (other$status == null) {
                            break label625;
                        }
                    } else if (this$status.equals(other$status)) {
                        break label625;
                    }

                    return false;
                }

                Object this$isDel = this.getIsDel();
                Object other$isDel = other.getIsDel();
                if (this$isDel == null) {
                    if (other$isDel != null) {
                        return false;
                    }
                } else if (!this$isDel.equals(other$isDel)) {
                    return false;
                }

                Object this$minPrice = this.getMinPrice();
                Object other$minPrice = other.getMinPrice();
                if (this$minPrice == null) {
                    if (other$minPrice != null) {
                        return false;
                    }
                } else if (!this$minPrice.equals(other$minPrice)) {
                    return false;
                }

                label604: {
                    Object this$minMarketPrice = this.getMinMarketPrice();
                    Object other$minMarketPrice = other.getMinMarketPrice();
                    if (this$minMarketPrice == null) {
                        if (other$minMarketPrice == null) {
                            break label604;
                        }
                    } else if (this$minMarketPrice.equals(other$minMarketPrice)) {
                        break label604;
                    }

                    return false;
                }

                Object this$maxPrice = this.getMaxPrice();
                Object other$maxPrice = other.getMaxPrice();
                if (this$maxPrice == null) {
                    if (other$maxPrice != null) {
                        return false;
                    }
                } else if (!this$maxPrice.equals(other$maxPrice)) {
                    return false;
                }

                Object this$maxMarketPrice = this.getMaxMarketPrice();
                Object other$maxMarketPrice = other.getMaxMarketPrice();
                if (this$maxMarketPrice == null) {
                    if (other$maxMarketPrice != null) {
                        return false;
                    }
                } else if (!this$maxMarketPrice.equals(other$maxMarketPrice)) {
                    return false;
                }

                label583: {
                    Object this$mainImage = this.getMainImage();
                    Object other$mainImage = other.getMainImage();
                    if (this$mainImage == null) {
                        if (other$mainImage == null) {
                            break label583;
                        }
                    } else if (this$mainImage.equals(other$mainImage)) {
                        break label583;
                    }

                    return false;
                }

                Object this$originalName = this.getOriginalName();
                Object other$originalName = other.getOriginalName();
                if (this$originalName == null) {
                    if (other$originalName != null) {
                        return false;
                    }
                } else if (!this$originalName.equals(other$originalName)) {
                    return false;
                }

                label569: {
                    Object this$translated = this.getTranslated();
                    Object other$translated = other.getTranslated();
                    if (this$translated == null) {
                        if (other$translated == null) {
                            break label569;
                        }
                    } else if (this$translated.equals(other$translated)) {
                        break label569;
                    }

                    return false;
                }

                Object this$categoryId = this.getCategoryId();
                Object other$categoryId = other.getCategoryId();
                if (this$categoryId == null) {
                    if (other$categoryId != null) {
                        return false;
                    }
                } else if (!this$categoryId.equals(other$categoryId)) {
                    return false;
                }

                label555: {
                    Object this$productId = this.getProductId();
                    Object other$productId = other.getProductId();
                    if (this$productId == null) {
                        if (other$productId == null) {
                            break label555;
                        }
                    } else if (this$productId.equals(other$productId)) {
                        break label555;
                    }

                    return false;
                }

                Object this$orginalMinPrice = this.getOrginalMinPrice();
                Object other$orginalMinPrice = other.getOrginalMinPrice();
                if (this$orginalMinPrice == null) {
                    if (other$orginalMinPrice != null) {
                        return false;
                    }
                } else if (!this$orginalMinPrice.equals(other$orginalMinPrice)) {
                    return false;
                }

                label541: {
                    Object this$orginalMinMarketPrice = this.getOrginalMinMarketPrice();
                    Object other$orginalMinMarketPrice = other.getOrginalMinMarketPrice();
                    if (this$orginalMinMarketPrice == null) {
                        if (other$orginalMinMarketPrice == null) {
                            break label541;
                        }
                    } else if (this$orginalMinMarketPrice.equals(other$orginalMinMarketPrice)) {
                        break label541;
                    }

                    return false;
                }

                label534: {
                    Object this$orginalMaxPrice = this.getOrginalMaxPrice();
                    Object other$orginalMaxPrice = other.getOrginalMaxPrice();
                    if (this$orginalMaxPrice == null) {
                        if (other$orginalMaxPrice == null) {
                            break label534;
                        }
                    } else if (this$orginalMaxPrice.equals(other$orginalMaxPrice)) {
                        break label534;
                    }

                    return false;
                }

                Object this$orginalMaxMarketPrice = this.getOrginalMaxMarketPrice();
                Object other$orginalMaxMarketPrice = other.getOrginalMaxMarketPrice();
                if (this$orginalMaxMarketPrice == null) {
                    if (other$orginalMaxMarketPrice != null) {
                        return false;
                    }
                } else if (!this$orginalMaxMarketPrice.equals(other$orginalMaxMarketPrice)) {
                    return false;
                }

                label520: {
                    Object this$labelId = this.getLabelId();
                    Object other$labelId = other.getLabelId();
                    if (this$labelId == null) {
                        if (other$labelId == null) {
                            break label520;
                        }
                    } else if (this$labelId.equals(other$labelId)) {
                        break label520;
                    }

                    return false;
                }

                label513: {
                    Object this$labelName = this.getLabelName();
                    Object other$labelName = other.getLabelName();
                    if (this$labelName == null) {
                        if (other$labelName == null) {
                            break label513;
                        }
                    } else if (this$labelName.equals(other$labelName)) {
                        break label513;
                    }

                    return false;
                }

                Object this$freightTemplateId = this.getFreightTemplateId();
                Object other$freightTemplateId = other.getFreightTemplateId();
                if (this$freightTemplateId == null) {
                    if (other$freightTemplateId != null) {
                        return false;
                    }
                } else if (!this$freightTemplateId.equals(other$freightTemplateId)) {
                    return false;
                }

                Object this$isFix = this.getIsFix();
                Object other$isFix = other.getIsFix();
                if (this$isFix == null) {
                    if (other$isFix != null) {
                        return false;
                    }
                } else if (!this$isFix.equals(other$isFix)) {
                    return false;
                }

                label492: {
                    Object this$updateType = this.getUpdateType();
                    Object other$updateType = other.getUpdateType();
                    if (this$updateType == null) {
                        if (other$updateType == null) {
                            break label492;
                        }
                    } else if (this$updateType.equals(other$updateType)) {
                        break label492;
                    }

                    return false;
                }

                Object this$tag = this.getTag();
                Object other$tag = other.getTag();
                if (this$tag == null) {
                    if (other$tag != null) {
                        return false;
                    }
                } else if (!this$tag.equals(other$tag)) {
                    return false;
                }

                Object this$updateTime = this.getUpdateTime();
                Object other$updateTime = other.getUpdateTime();
                if (this$updateTime == null) {
                    if (other$updateTime != null) {
                        return false;
                    }
                } else if (!this$updateTime.equals(other$updateTime)) {
                    return false;
                }

                label471: {
                    Object this$sales = this.getSales();
                    Object other$sales = other.getSales();
                    if (this$sales == null) {
                        if (other$sales == null) {
                            break label471;
                        }
                    } else if (this$sales.equals(other$sales)) {
                        break label471;
                    }

                    return false;
                }

                Object this$channel = this.getChannel();
                Object other$channel = other.getChannel();
                if (this$channel == null) {
                    if (other$channel != null) {
                        return false;
                    }
                } else if (!this$channel.equals(other$channel)) {
                    return false;
                }

                label457: {
                    Object this$appChannel = this.getAppChannel();
                    Object other$appChannel = other.getAppChannel();
                    if (this$appChannel == null) {
                        if (other$appChannel == null) {
                            break label457;
                        }
                    } else if (this$appChannel.equals(other$appChannel)) {
                        break label457;
                    }

                    return false;
                }

                Object this$parentId = this.getParentId();
                Object other$parentId = other.getParentId();
                if (this$parentId == null) {
                    if (other$parentId != null) {
                        return false;
                    }
                } else if (!this$parentId.equals(other$parentId)) {
                    return false;
                }

                label443: {
                    Object this$country = this.getCountry();
                    Object other$country = other.getCountry();
                    if (this$country == null) {
                        if (other$country == null) {
                            break label443;
                        }
                    } else if (this$country.equals(other$country)) {
                        break label443;
                    }

                    return false;
                }

                Object this$orginalSales = this.getOrginalSales();
                Object other$orginalSales = other.getOrginalSales();
                if (this$orginalSales == null) {
                    if (other$orginalSales != null) {
                        return false;
                    }
                } else if (!this$orginalSales.equals(other$orginalSales)) {
                    return false;
                }

                label429: {
                    Object this$score = this.getScore();
                    Object other$score = other.getScore();
                    if (this$score == null) {
                        if (other$score == null) {
                            break label429;
                        }
                    } else if (this$score.equals(other$score)) {
                        break label429;
                    }

                    return false;
                }

                label422: {
                    Object this$sortValue = this.getSortValue();
                    Object other$sortValue = other.getSortValue();
                    if (this$sortValue == null) {
                        if (other$sortValue == null) {
                            break label422;
                        }
                    } else if (this$sortValue.equals(other$sortValue)) {
                        break label422;
                    }

                    return false;
                }

                Object this$shopId = this.getShopId();
                Object other$shopId = other.getShopId();
                if (this$shopId == null) {
                    if (other$shopId != null) {
                        return false;
                    }
                } else if (!this$shopId.equals(other$shopId)) {
                    return false;
                }

                label408: {
                    Object this$shopName = this.getShopName();
                    Object other$shopName = other.getShopName();
                    if (this$shopName == null) {
                        if (other$shopName == null) {
                            break label408;
                        }
                    } else if (this$shopName.equals(other$shopName)) {
                        break label408;
                    }

                    return false;
                }

                label401: {
                    Object this$weight = this.getWeight();
                    Object other$weight = other.getWeight();
                    if (this$weight == null) {
                        if (other$weight == null) {
                            break label401;
                        }
                    } else if (this$weight.equals(other$weight)) {
                        break label401;
                    }

                    return false;
                }

                Object this$packageSzie = this.getPackageSzie();
                Object other$packageSzie = other.getPackageSzie();
                if (this$packageSzie == null) {
                    if (other$packageSzie != null) {
                        return false;
                    }
                } else if (!this$packageSzie.equals(other$packageSzie)) {
                    return false;
                }

                Object this$propertyList = this.getPropertyList();
                Object other$propertyList = other.getPropertyList();
                if (this$propertyList == null) {
                    if (other$propertyList != null) {
                        return false;
                    }
                } else if (!this$propertyList.equals(other$propertyList)) {
                    return false;
                }

                label380: {
                    Object this$itemList = this.getItemList();
                    Object other$itemList = other.getItemList();
                    if (this$itemList == null) {
                        if (other$itemList == null) {
                            break label380;
                        }
                    } else if (this$itemList.equals(other$itemList)) {
                        break label380;
                    }

                    return false;
                }

                Object this$propertyValues = this.getPropertyValues();
                Object other$propertyValues = other.getPropertyValues();
                if (this$propertyValues == null) {
                    if (other$propertyValues != null) {
                        return false;
                    }
                } else if (!this$propertyValues.equals(other$propertyValues)) {
                    return false;
                }

                Object this$propertyValueList = this.getPropertyValueList();
                Object other$propertyValueList = other.getPropertyValueList();
                if (this$propertyValueList == null) {
                    if (other$propertyValueList != null) {
                        return false;
                    }
                } else if (!this$propertyValueList.equals(other$propertyValueList)) {
                    return false;
                }

                label359: {
                    Object this$goodsImageList = this.getGoodsImageList();
                    Object other$goodsImageList = other.getGoodsImageList();
                    if (this$goodsImageList == null) {
                        if (other$goodsImageList == null) {
                            break label359;
                        }
                    } else if (this$goodsImageList.equals(other$goodsImageList)) {
                        break label359;
                    }

                    return false;
                }

                Object this$goodsExtDetailImgList = this.getGoodsExtDetailImgList();
                Object other$goodsExtDetailImgList = other.getGoodsExtDetailImgList();
                if (this$goodsExtDetailImgList == null) {
                    if (other$goodsExtDetailImgList != null) {
                        return false;
                    }
                } else if (!this$goodsExtDetailImgList.equals(other$goodsExtDetailImgList)) {
                    return false;
                }

                label345: {
                    Object this$likeName = this.getLikeName();
                    Object other$likeName = other.getLikeName();
                    if (this$likeName == null) {
                        if (other$likeName == null) {
                            break label345;
                        }
                    } else if (this$likeName.equals(other$likeName)) {
                        break label345;
                    }

                    return false;
                }

                Object this$startMinPrice = this.getStartMinPrice();
                Object other$startMinPrice = other.getStartMinPrice();
                if (this$startMinPrice == null) {
                    if (other$startMinPrice != null) {
                        return false;
                    }
                } else if (!this$startMinPrice.equals(other$startMinPrice)) {
                    return false;
                }

                label331: {
                    Object this$endMinPrice = this.getEndMinPrice();
                    Object other$endMinPrice = other.getEndMinPrice();
                    if (this$endMinPrice == null) {
                        if (other$endMinPrice == null) {
                            break label331;
                        }
                    } else if (this$endMinPrice.equals(other$endMinPrice)) {
                        break label331;
                    }

                    return false;
                }

                Object this$orderBy = this.getOrderBy();
                Object other$orderBy = other.getOrderBy();
                if (this$orderBy == null) {
                    if (other$orderBy != null) {
                        return false;
                    }
                } else if (!this$orderBy.equals(other$orderBy)) {
                    return false;
                }

                label317: {
                    Object this$sortKey = this.getSortKey();
                    Object other$sortKey = other.getSortKey();
                    if (this$sortKey == null) {
                        if (other$sortKey == null) {
                            break label317;
                        }
                    } else if (this$sortKey.equals(other$sortKey)) {
                        break label317;
                    }

                    return false;
                }

                label310: {
                    Object this$pageSize = this.getPageSize();
                    Object other$pageSize = other.getPageSize();
                    if (this$pageSize == null) {
                        if (other$pageSize == null) {
                            break label310;
                        }
                    } else if (this$pageSize.equals(other$pageSize)) {
                        break label310;
                    }

                    return false;
                }

                Object this$pageNow = this.getPageNow();
                Object other$pageNow = other.getPageNow();
                if (this$pageNow == null) {
                    if (other$pageNow != null) {
                        return false;
                    }
                } else if (!this$pageNow.equals(other$pageNow)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof GoodsESModel;
    }


    public String toString() {
        return "GoodsESModel(id=" + this.getId() + ", itemNumber=" + this.getItemNumber() + ", name=" + this.getName() + ", orginalName=" + this.getOrginalName() + ", type=" + this.getType() + ", isMultiBrand=" + this.getIsMultiBrand() + ", code=" + this.getCode() + ", areaCode=" + this.getAreaCode() + ", isShow=" + this.getIsShow() + ", createTime=" + this.getCreateTime() + ", status=" + this.getStatus() + ", isDel=" + this.getIsDel() + ", minPrice=" + this.getMinPrice() + ", minMarketPrice=" + this.getMinMarketPrice() + ", maxPrice=" + this.getMaxPrice() + ", maxMarketPrice=" + this.getMaxMarketPrice() + ", mainImage=" + this.getMainImage() + ", originalName=" + this.getOriginalName() + ", translated=" + this.getTranslated() + ", categoryId=" + this.getCategoryId() + ", productId=" + this.getProductId() + ", orginalMinPrice=" + this.getOrginalMinPrice() + ", orginalMinMarketPrice=" + this.getOrginalMinMarketPrice() + ", orginalMaxPrice=" + this.getOrginalMaxPrice() + ", orginalMaxMarketPrice=" + this.getOrginalMaxMarketPrice() + ", labelId=" + this.getLabelId() + ", labelName=" + this.getLabelName() + ", freightTemplateId=" + this.getFreightTemplateId() + ", isFix=" + this.getIsFix() + ", updateType=" + this.getUpdateType() + ", tag=" + this.getTag() + ", updateTime=" + this.getUpdateTime() + ", sales=" + this.getSales() + ", channel=" + this.getChannel() + ", appChannel=" + this.getAppChannel() + ", parentId=" + this.getParentId() + ", country=" + this.getCountry() + ", orginalSales=" + this.getOrginalSales() + ", score=" + this.getScore() + ", sortValue=" + this.getSortValue() + ", shopId=" + this.getShopId() + ", shopName=" + this.getShopName() + ", weight=" + this.getWeight() + ", packageSzie=" + this.getPackageSzie() + ", propertyList=" + this.getPropertyList() + ", itemList=" + this.getItemList() + ", propertyValues=" + this.getPropertyValues() + ", propertyValueList=" + this.getPropertyValueList() + ", goodsImageList=" + this.getGoodsImageList() + ", goodsExtDetailImgList=" + this.getGoodsExtDetailImgList() + ", likeName=" + this.getLikeName() + ", startMinPrice=" + this.getStartMinPrice() + ", endMinPrice=" + this.getEndMinPrice() + ", orderBy=" + this.getOrderBy() + ", sortKey=" + this.getSortKey() + ", pageSize=" + this.getPageSize() + ", pageNow=" + this.getPageNow() + ")";
    }
}
