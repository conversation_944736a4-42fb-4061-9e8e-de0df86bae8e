package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * ShopTagDTO
 *
 * <AUTHOR>
 * &#064;date  2025
 */

@Data
@ApiModel("店铺标签查询入参")
public class ShopTagInput implements Serializable {

    @ApiModelProperty("标签名称 模糊查询")
    private String name;

    @ApiModelProperty("标签id，列表查询，可为空")
    private List<Long> tagIds;
}
