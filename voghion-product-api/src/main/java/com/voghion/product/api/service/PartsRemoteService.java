package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.model.vo.PartsTemplateVO;
import com.voghion.product.model.vo.VehicleListImportVO;
import com.voghion.product.model.vo.condition.PartsCondition;

import java.util.List;
import java.util.Map;

public interface PartsRemoteService {

    /**
     * 添加车型适配模板
     * @param partsTemplateVO
     */
    Result<Boolean> addTemplate(PartsTemplateVO partsTemplateVO);

    /**
     * 更新车型适配模板
     * @param partsTemplateVO
     */
    Result<Boolean> updateTemplate(PartsTemplateVO partsTemplateVO);

    /**
     * 删除车型适配模板
     * @param id
     */
    Result<Boolean> deleteTemplate(Long id);

    /**
     * 获取车型适配配置
     * @param partsCondition
     * @return
     */
    Result<PageView<PartsTemplateVO>> queryPageByCondition(PartsCondition partsCondition);

    /**
     * 根据country make model year获取车型映射表
     * @param partsCondition
     * @return
     */
    Result<Map<String, ?>> listTemplateByType(PartsCondition partsCondition);

    /**
     * 根据country 逐个获取 make model year
     * @param partsCondition
     * @return
     */
    Result<List<String>> getSelect(PartsCondition partsCondition);

    /**
     * 导入车型
     * @param list
     * @param type
     * @return
     */
    Result<List<?>> importVehicle(List<VehicleListImportVO> list, Integer type);
}
