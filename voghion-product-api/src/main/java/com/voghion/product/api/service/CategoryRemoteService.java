package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.ShopCategoryMainDto;
import com.voghion.product.api.input.FrontCategoryInputVO;
import com.voghion.product.api.output.*;
import com.voghion.product.model.dto.FirstCategoryDto;
import com.voghion.product.model.po.Category;
import com.voghion.product.model.vo.CategoryStandardVO;

import java.util.List;
import java.util.Map;

public interface CategoryRemoteService {


    Result<List<CategoryVO>> queryChildCategoryByParentId(Long parentId);
    Result<Map<Long,List<Long>>> queryChildCategoryIdByParentIds(List<Long> parentIds);

    Result<Map<Long,List<Long>>> queryChildCategoryIdByParentIdsNew(List<Long> parentIds);
    Result<Map<Long,List<CategoryVO>>> queryChildCategoryByParentIds(List<Long> parentIds);

    //根据前台类目id查询所有包括自身的后台子类目id集合
    Result<List<Long>> queryChildCategoryIdByFrontCateId(Long frontId);
    /**
     * 查询首页类目列表
     * @param input
     * @return
     */
    Result<List<FrontCategoryVO>> findFrontCategory(FrontCategoryInputVO input);

    /**
     * 根据前台类型查询后台类目
     * @param frontCategoryInfoVO
     * @return
     */
    Result<FrontCategoryInfoVO> findCategoryIdsByFrontCategoryids(FrontCategoryInfoVO frontCategoryInfoVO);

    /**
     * 根据性别权重进行排序返回首页类目信息
     * @param input
     * @return
     */
    Result<List<FrontCategoryVO>> findIndexFrontCategory(FrontCategoryInputVO input);

    Result<List<CategoryVO>> queryCategoryByIds(List<Long> categoryIds);

    Result<List<CategoryVO>> queryAllCategory();

    Result<List<CategoryVO>> queryAllByParentCategoryId(Long categoryId);

    Result<Map<Long, String>> getCategoryPathByIds(List<Long> categoryIds);

    List<Long> queryAllChildCategoryIdsByCategoryNamesOrKeyword(List<String> keywords);
    /**
     * 查询满足关键字条件的类目及子类目id列表
     * @param keywords 关键字
     * @param queryType 查询类型，0-查询所有(匹配类目名称或者类目关联关键词)，1-只匹配类目名称，2-只匹配类目关联关键词
     * @return
     */
    List<Long> queryAllChildCategoryIdsByCategoryNamesOrKeyword(List<String> keywords, int queryType);

    Result<List<CategoryVO>> queryRelateCategoryByIds(Long categoryId);
    Result<List<Long>> queryRelateCategoryIds(Long categoryId);

    List<CategoryAdminOutput> queryCategoryByPid(Long pid);


    Long queryParentCategoryIdByCategoryId(Long categoryId);

    /**
     * 根据类目名称查找所有一级类目
     * @param categoryNames
     * @return
     */
    Result<List<CategoryVO>> queryAllOneLevelByCategoryNames(List<String> categoryNames);

    ShopCategoryMainDto queryShopCategoryMain(Long shopId);

    //获取多个店铺的主营类型信息
    ShopCategoryMainDto queryShopsCategoryMain(List<Long> shopIds);

    List<FrontCategoryDTO> queryShopFrontCategoryList(Long shopId);

    /**
     * 根据关键词查询前台类目ids
     */
    List<FrontCategoryVO> queryAllFrontCategoryIdsByKeyword(String keyword);

    /**
     * 根据前台类目ids查询对应的所有后台类目及其子类目
     */
    List<Long> queryChildCategoryIdsByFrontCategoryIds(List<Long> frontCategoryIds);

    /**
     * 根据后台类目获取叶子类目信息
     * @param categoryIds
     * @return
     */
    Result<List<Long>> queryChildCategoryIdsByCategoryIds(List<Long> categoryIds);

    CategoryStandardVO checkCategory(Long categoryId, Long shopId);

    CategoryVO getById(Long categoryId);

    /**
     * 根据类目id查询类目信息
     * @param categoryIds
     * @return
     */
    Result<List<CategoryVO>> queryCategoryInfoByIds(List<Long> categoryIds);

    /**
     * 获取所有的一级类目
     * @return
     */
    Result<List<CategoryVO>> queryAllOneLevel();


    /**
     * 根据类目id获取名称和一级类目id及名称
     */
    Result<Map<Long, FirstCategoryDto>> queryFirstCategoryInfo(List<Long> categoryIds);
}
