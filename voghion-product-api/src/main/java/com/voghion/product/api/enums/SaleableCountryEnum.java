package com.voghion.product.api.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public enum SaleableCountryEnum {
    FR("France", "法国"),
    DE("Germany", "德国"),
    IT("Italy", "意大利"),
    ES("Spain", "西班牙"),
    GB("United Kingdom", "英国"),

    PL("Poland", "波兰"),
    CZ("Czech Republic", "捷克"),
    SK("Slovakia", "斯洛伐克"),
    BE("Belgium", "比利时"),
    NL("Netherlands", "荷兰"),

    CH("Switzerland", "瑞士"),
    DK("Denmark", "丹麦"),
    PT("Portugal", "葡萄牙"),
    AT("Austria", "奥地利"),
    NO("Norway", "挪威"),
    SE("Sweden", "瑞典"),

    EE("Estonia", "爱沙尼亚"),
    FI("Finland", "芬兰"),
    HR("Croatia", "克罗地亚"),
    LV("Latvia", "拉脱维亚"),
    LT("Lithuania", "立陶宛"),

    SI("Slovenia", "斯洛文尼亚"),
    GR("Greece", "希腊"),
    LU("Luxembourg", "卢森堡"),
    HU("Hungary", "匈牙利"),
    IS("Iceland", "冰岛"),

    RO("Romania", "罗马尼亚"),
    BG("Bulgaria", "保加利亚"),
    AU("Australia", "澳大利亚"),
    NZ("New Zealand", "新西兰"),
    US("United States", "美国"),
    CA("CANADA","加拿大"),
//    HK("Hong Kong", "中国香港"),
//    PH("Philippines", "菲律宾"),
    ;

    private String nameEn;
    private String nameCn;

    public String getNameEn() {
        return nameEn;
    }

    public String getNameCn() {
        return nameCn;
    }

    SaleableCountryEnum(String nameEn, String nameCn) {
        this.nameEn = nameEn;
        this.nameCn = nameCn;
    }

    public static void main(String[] args) {
        System.out.println(SaleableCountryEnum.DE.name());
    }

    public static String getCnNameByCode(String code) {
        for (SaleableCountryEnum value : SaleableCountryEnum.values()) {
            if (value.name().equals(code)) {
                return value.getNameCn();
            }
        }
        return code;
    }
    public static Optional<SaleableCountryEnum> getByCnName(String cnName){
       return Arrays.stream(SaleableCountryEnum.values())
               .filter(countryEnum -> Objects.equals(countryEnum.getNameCn(), cnName))
               .findAny();
    }

    /**
     * 获取欧洲国家
     */
    public static String getEUCountryCnNames() {
        return Arrays.stream(SaleableCountryEnum.values())
                .filter(enums -> enums != US && enums != AU && enums != GB && enums != NZ)
                .map(SaleableCountryEnum::getNameCn)
                .collect(Collectors.joining(","));

    }

    /**
     * 获取欧洲国家
     */
    public static String getEUCountryEnNames() {
        return Arrays.stream(SaleableCountryEnum.values())
                .filter(enums -> enums != US && enums != AU && enums != GB && enums != NZ)
                .map(SaleableCountryEnum::name)
                .collect(Collectors.joining(","));

    }

}
