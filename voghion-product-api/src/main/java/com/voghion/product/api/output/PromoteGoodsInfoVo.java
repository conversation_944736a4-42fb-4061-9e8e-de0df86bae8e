package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @date: 2022/8/8 下午4:09
 * @author: jashley
 */
@Data
public class PromoteGoodsInfoVo implements Serializable {
    private static final long serialVersionUID = -865326535120885949L;
    private Long goodsId;
    private String mainImage;
    private String name;
    private Long categoryId;
    private String category;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Integer isShow;
    private Date createTime;
    private List<GoodsFreightVO> freightList;
}
