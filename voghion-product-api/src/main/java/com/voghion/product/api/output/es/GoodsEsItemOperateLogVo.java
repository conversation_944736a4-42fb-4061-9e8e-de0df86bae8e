//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output.es;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class GoodsEsItemOperateLogVo implements Serializable {
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 商家skuId
     */
    private Long skuId;
    /**
     * SKU名称
     */
    private String name;
    /**
     * 操作类型 0新增 1修改 2删除
     */
    private Integer opt;

    /**
     * 修改时间
     */
    private String createTime;


    private UpdateInfo newInfo;
    private UpdateInfo oldInfo;

    public GoodsEsItemOperateLogVo(GoodsEsItemVo vo) {
        if (null == vo) {
            return;
        }
        this.goodsId = vo.getGoodsId();
        this.skuId = vo.getSkuId();
        this.name = vo.getName();
        this.opt = vo.getOpt();
        this.createTime = vo.getCreateTime();

        switch (vo.getOpt()) {
            case 0:
            case 1:
                this.newInfo = new UpdateInfo(vo);
                break;
            case 2:
                this.oldInfo = new UpdateInfo(vo);
                break;
            default:
                break;
        }
    }

    public GoodsEsItemOperateLogVo(GoodsEsItemVo oldInfo, GoodsEsItemVo newInfo) {
        if (null == oldInfo || null == newInfo) {
            return;
        }
        this.goodsId = newInfo.getGoodsId();
        this.skuId = newInfo.getSkuId();
        this.name = newInfo.getName();
        this.opt = newInfo.getOpt();
        this.createTime = newInfo.getCreateTime();
        this.newInfo = new UpdateInfo(oldInfo);
        this.oldInfo = new UpdateInfo(newInfo);
    }


    @Data
    public static class UpdateInfo implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        /**
         * 默认运费
         */
        private BigDecimal defaultDelivery;
        /**
         * 价格
         */
        private BigDecimal orginalPrice;
        /**
         * 库存
         */
        private Integer stock;
        private String updateTime;

        public UpdateInfo(GoodsEsItemVo vo) {
            this.defaultDelivery = vo.getDefaultDelivery();
            this.orginalPrice = vo.getOrginalPrice();
            this.stock = vo.getStock();
            this.updateTime = vo.getUpdateTime();
        }
    }


}
