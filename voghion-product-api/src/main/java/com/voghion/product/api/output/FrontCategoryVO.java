package com.voghion.product.api.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/31 17:29
 * @describe
 */
@Data
@ApiModel
public class FrontCategoryVO implements Serializable {


    /**
     * 前台类目id
     */
    @ApiModelProperty(value = "前台类目id")
    private Long id;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "前台类目名称")
    private String name;

    /**
     * 跳转类型 4-类目，13-自定义商品列表
     */
    @ApiModelProperty(value = "跳转类型 4-类目，13-自定义商品列表")
    private Integer type;


    @ApiModelProperty(value = "type描述")
    private String typeDesc;

    /**
     * 跳转值
     */
    @ApiModelProperty(value = "传入的标签数据")
    private String value;


    /**
     * 父节点
     */
    @ApiModelProperty(value = "前台类目 父节点")
    private Long parentId;

    /**
     * 类目等级
     */
    @ApiModelProperty(value = "类目等级")
    private Integer level;

    /**
     * 一级类目图标
     */
    @ApiModelProperty(value = "类目图标")
    private String icon;

    /**
     * 选中图标
     */
    @ApiModelProperty(value = "选中图标")
    private String selectIcon;

    /**
     * 排序越小越前面
     */
    @ApiModelProperty(value = "排序越小越前面")
    private Integer sort;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * appChannel
     */
    @ApiModelProperty(value = "app渠道")
    private String appChannel;

    /**
     * 子类目
     */
    @ApiModelProperty(value = "子类目")
    private List<FrontCategoryVO> childCategory;


    /**
     * 组合的类目信息
     */
    @ApiModelProperty(value = "组合的类目信息")
    private String categoryName;


    @ApiModelProperty(value = "显示的类目名称")
    private List<FrontCategoryValueVO> values;

    @ApiModelProperty(value = "跳转路由")
    private String routeUrl;

    @ApiModelProperty(value = "版本号")
    private String manageVersion;

    @ApiModelProperty(value = "是否固定icon 0:否 1是")
    private Integer optionBitmask;

}
