package com.voghion.product.api.output;


import com.colorlight.base.model.vo.BaseVO;
import com.voghion.product.api.enums.DataTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公用内容模块对象
 * <AUTHOR>
 * @date 2021/03/23
 */
@Data
@ApiModel
public class ContentBaseVO extends BaseVO {


    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * 站点
     */
    @ApiModelProperty(value = "站点")
    private String site;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "跳转类型")
    private String type;


    /**
     * 数据值
     */
    @ApiModelProperty(value = "数据内容")
    private String value;


    /**
     * 图片地址
     */
    @ApiModelProperty(value ="图片地址")
    private String imgUrl;

    @ApiModelProperty(value =  "标题")
    private String title;


    @ApiModelProperty(value ="子标题")
    private String subTitle;


    @ApiModelProperty("排序字段 ")
    private Integer sort;

    /**
     * 构造模板内容无图片
     * @param site  站点信息
     * @param dataTypeEnums
     * @param value
     */
    public ContentBaseVO(String site, DataTypeEnums dataTypeEnums, String value){
        this.site=site;
        this.dataType   =   dataTypeEnums.getModuleItemType();
        this.type       =   dataTypeEnums.getFrontType();
        this.value      =   value;
    }

    /**
     * 构造模板内容
     * @param site  站点信息
     * @param dataTypeEnums
     * @param value
     */
    public ContentBaseVO(String site, DataTypeEnums dataTypeEnums, String value,String imgUrl,String tile){
        this.site=site;
        this.dataType   =   dataTypeEnums.getModuleItemType();
        this.type       =   dataTypeEnums.getFrontType();
        this.value      =   value;
        this.imgUrl     =   imgUrl;
        this.title =   tile;
    }


    /**
     * 构造模板内容
     * @param site  站点信息
     * @param dataTypeEnums
     * @param value
     */
    public ContentBaseVO(String site, DataTypeEnums dataTypeEnums, String value,String imgUrl,String tile,String subTitle){
        this.site=site;
        this.dataType   =   dataTypeEnums.getModuleItemType();
        this.type       =   dataTypeEnums.getFrontType();
        this.value      =   value;
        this.imgUrl     =   imgUrl;
        this.title =   tile;
        this.subTitle   =   subTitle;
    }

    /**
     * 构造模板内容
     * @param site  站点信息
     * @param dataTypeEnums
     * @param value
     */
    public ContentBaseVO(String site, DataTypeEnums dataTypeEnums, String value,String imgUrl){
        this.site=site;
        this.dataType   =   dataTypeEnums.getModuleItemType();
        this.type       =   dataTypeEnums.getFrontType();
        this.value      =   value;
        this.imgUrl     =   imgUrl;
    }

    public ContentBaseVO(){

    }
}