package com.voghion.product.api.output;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 前台类目表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public class FrontCategoryDTO implements Serializable {


    /**
     * 分类id
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 跳转类型 4-类目，13-自定义商品列表
     */
    private Integer type;

    /**
     * 数据
     */
    private String value;

    /**
     * 父类ID 0表示当前类目为1级类目
     */
    private Long parentId;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 图标
     */
    private String icon;

    /**
     * 选择图标
     */
    private String selectIcon;

    /**
     * 排序规则，越小越靠前
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDel;

    /**
     * 国别
     */
    private String country;

    /**
     * appChannel
     */
    private String appChannel;


    /**
     * 性别
     */
    private Integer gender;

    /**
     * 男权重
     */
    private String maleWeight;

    /**
     * 女权重
     */
    private String femaleWeight;

    /**
     * 通用权重
     */
    private String generalWeight;


    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getAppChannel() {
        return appChannel;
    }

    public void setAppChannel(String appChannel) {
        this.appChannel = appChannel;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getMaleWeight() {
        return maleWeight;
    }

    public void setMaleWeight(String maleWeight) {
        this.maleWeight = maleWeight;
    }

    public String getFemaleWeight() {
        return femaleWeight;
    }

    public void setFemaleWeight(String femaleWeight) {
        this.femaleWeight = femaleWeight;
    }

    public String getGeneralWeight() {
        return generalWeight;
    }

    public void setGeneralWeight(String generalWeight) {
        this.generalWeight = generalWeight;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
    public String getSelectIcon() {
        return selectIcon;
    }

    public void setSelectIcon(String selectIcon) {
        this.selectIcon = selectIcon;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }


    @Override
    public String toString() {
        return "FrontCategory{" +
            "id=" + id +
            ", name=" + name +
            ", type=" + type +
            ", value=" + value +
            ", parentId=" + parentId +
            ", level=" + level +
            ", icon=" + icon +
            ", selectIcon=" + selectIcon +
            ", sort=" + sort +
            ", createTime=" + createTime +
            ", isDel=" + isDel +
        "}";
    }
}
