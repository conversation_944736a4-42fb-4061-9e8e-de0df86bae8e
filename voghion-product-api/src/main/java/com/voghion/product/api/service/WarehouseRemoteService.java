package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.WarehouseInfoDto;
import com.voghion.product.api.output.WarehouseOverseasConfigVo;

import java.util.List;

public interface WarehouseRemoteService {
    Result<WarehouseInfoDto> findWarehouseInfoByShopId(Long shopId);

    Result<List<WarehouseOverseasConfigVo>> queryListByShopIdsAndCountry(List<Long> shopIds, String country);
}
