package com.voghion.product.api.enums;

import lombok.Getter;

/**
 * GoodsTagEnum
 *
 * <AUTHOR>
 * @date 2022/12/17
 */
@Getter
public enum GoodsTagTypeEnum {
    /**
     * 1 flashdel 2 七日达 3空着不用 4海外仓 5满减减钱 6满减打折 7虚拟列表 8精选店铺 9品牌
     */
    //商品标签
    FLASH_DEAL(1, "flashdel"),
    SEVEN_DAY(2, "七日达"),
    WAREHOUSE(4, "海外仓"),
    REDUCE_MONET(5, "满减减钱"),
    REDUCE_DISCOUNT(6, "满减打折"),
    CUSTOM_LIST(7, "虚拟列表"),
    BRAND(9, "品牌"),
    HONG_KONG(10, "香港发货"),
    BUY_NOW_PAY_LATER(11, "先买后付"),
    NEGATIVE(13, "负向业务(知识产权类)"),
    UN_NEGATIVE(14, "负向业务(违禁品类)"),
    COUPON(15, "优惠券"),
    RATE_LIMTING(16, "商品限流"),
    WHOLESALE(17, "批发"),
    TREND(18, "趋势"),
    POSITIVE(19, "正向业务"),

    //店铺标签
    SHOP(12, "店铺标签"),
    SELECT_SHOP(8, "精选店铺"),
    SHOP_SERVER(20, "店铺业务标签"),
    ;

    private final Integer type;
    private final String desc;

    GoodsTagTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static GoodsTagTypeEnum getEnumByType(Integer type) {
        for (GoodsTagTypeEnum p : GoodsTagTypeEnum.values()) {
            if (p.getType().equals(type)) {
                return p;
            }
        }
        return null;
    }
}
