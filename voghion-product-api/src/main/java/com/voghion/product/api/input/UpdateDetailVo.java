package com.voghion.product.api.input;

import com.voghion.product.model.vo.GoodsExtraPropertyVO;
import com.voghion.product.model.vo.GoodsManualVO;
import com.voghion.product.model.vo.GoodsPartsVO;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class UpdateDetailVo implements Serializable {
    private static final long serialVersionUID = -8848237628195533446L;

    @ApiModelProperty(value = "商品id", required = true)
    private Long goodsId;

    @ApiModelProperty(value = "商品名称", required = true)
    private String name;

    @ApiModelProperty(value = "类目id", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "供应商商品id", required = true)
    private String itemCode;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "商品主图", required = true)
    private List<String> goodsImages;

    @ApiModelProperty("商品视频")
    private List<String> goodsVideos;

    @ApiModelProperty("尺码表模板id")
    private Long sizeChartTemplateId;

    @ApiModelProperty("尺码图")
    private String sizeImage;

    @ApiModelProperty("详情文字")
    private String description;

    @ApiModelProperty(value = "详情图", required = true)
    private List<String> detailsImgs;

    @ApiModelProperty("属性详情")
    private List<PropertyGoodsInfoVO> propertyGoodsInfoVOS;

//    @ApiModelProperty("属性模板详情")
//    private List<PropertyDetailInfoVO> propertyDetailInfoVOList;

    @ApiModelProperty("自定义属性")
    private List<GoodsExtraPropertyVO> goodsExtraPropertyVOS;

    @ApiModelProperty("原始店铺名称(来源供应商)")
    private String originalShopName;

    @ApiModelProperty("商品原始链接")
    private String goodsUrl;

    @ApiModelProperty("采购供应商")
    private String procureSupplier;

    @ApiModelProperty("商品采购链接")
    private String costUrl;

    private Long shopId;

    @ApiModelProperty("商品实拍图")
    private List<String> realShotImgs;

    /**
     * 操作日志
     * @see com.voghion.product.api.enums.OperationLogTypeEnums
     */
    private Integer operationLogType;

    /**
     * 国家商品说明书详情
     */
    @ApiModelProperty("国家商品说明书详情")
    private List<GoodsManualVO> goodsManualVOList;

    @ApiModelProperty("说明视频")
    private List<GoodsManualVO> goodsManualVideoList;

    @ApiModelProperty("国家汽配")
    private List<GoodsPartsVO> goodsPartsVOList;
}
