package com.voghion.product.api.output;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 属性表
 * </p>
 *
 * <AUTHOR> @since 2021-11-04
 */
public class PropertyESModel implements Serializable {


    private Long id;

    //属性名
    private String name;

    private Long categoryId;

    //别名
    private String aliasName;

    //是否枚举 0表示false 1表示true
    private String isEnums;

    //是否输入属性 0表示false 1表示true
    private String isInput;

    //是否必须 0表示false 1表示true
    private String isNeed;

    //是否多选 0表示false 1表示true
    private String isMulti;

    //属性类别 1表示关键属性 2表示销售属性 3表示特殊属性
    private String propertyType;

    //排序 数字越小越靠前
    private Integer sort;

    //创建时间
    private LocalDateTime createTime;

    //状态 1表示正常 99表示禁用
    private Integer status;

    //原始属性名称
    private String originalName;

    //是否已翻译，0:未翻译，1:已翻译
    private Integer translated;

    //属性值列表
    private List<PropertyValueESModel> propertyValueESModelList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getIsEnums() {
        return isEnums;
    }

    public void setIsEnums(String isEnums) {
        this.isEnums = isEnums;
    }

    public String getIsInput() {
        return isInput;
    }

    public void setIsInput(String isInput) {
        this.isInput = isInput;
    }

    public String getIsNeed() {
        return isNeed;
    }

    public void setIsNeed(String isNeed) {
        this.isNeed = isNeed;
    }

    public String getIsMulti() {
        return isMulti;
    }

    public void setIsMulti(String isMulti) {
        this.isMulti = isMulti;
    }

    public String getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(String propertyType) {
        this.propertyType = propertyType;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public Integer getTranslated() {
        return translated;
    }

    public void setTranslated(Integer translated) {
        this.translated = translated;
    }

    public List<PropertyValueESModel> getPropertyValueESModelList() {
        return propertyValueESModelList;
    }

    public void setPropertyValueESModelList(List<PropertyValueESModel> propertyValueESModelList) {
        this.propertyValueESModelList = propertyValueESModelList;
    }
}
