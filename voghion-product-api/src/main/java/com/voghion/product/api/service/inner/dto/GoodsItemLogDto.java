package com.voghion.product.api.service.inner.dto;//package com.voghion.product.inner.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsItemLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * SKU ID
     */
    private Long skuId;


    /**
     * 原始价格
     */
    private BigDecimal orginalPrice;

    /**
     * 库存数量
     */
    private Long stock;

    /**
     * SKU名称
     */
    private String name;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 修改时间
     */
    private String createTime;


    private String updateBy;

    private BigDecimal defaultDelivery;

    private int opt;


}
