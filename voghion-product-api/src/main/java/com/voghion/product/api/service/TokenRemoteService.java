package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.input.QueryGoodsDTO;
import com.voghion.product.api.input.QueryTokenDTO;
import com.voghion.product.api.output.GoodsListVO;
import com.voghion.product.api.output.GoodsOutput;
import com.voghion.product.api.output.MerchantsTokenOutput;
import com.voghion.product.model.vo.GoodsDetailInfoVO;
import com.voghion.product.model.vo.ProductInfoInput;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2021/11/24
 * @describe
 */
public interface TokenRemoteService {

    /**
     * 查询shopId与ShopName
     * @param queryTokenDTO
     * @return
     */
    Result<MerchantsTokenOutput> queryShopId(QueryTokenDTO queryTokenDTO);

}
