package com.voghion.product.api.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @description:
 * @date: 2022/10/21 下午3:25
 * @author: jashley
 */
public enum OperationLogTypeEnums {

    //todo 0~999 商品新增相关
    ADD_GOODS(11, "后台新增商品"),
    IMPORT_GOODS(12, "商家Excel导入商品"),
    ERP_ADD_GOODS(13, "erp新增商品"),
    ALIEXPRESS_ADD_GOODS(14, "速卖通导入商品"),
    AE_IMPORT_GOODS(15, "根据AE国际的excel导入商品"),
    FANNO_IMPORT_GOODS(16, "根据Fanno的excel导入商品"),
    COPY_GOODS(17, "复制商品"),
    SCM_ADD_GOODS(18, "scm供应商商品同步新增"),
    DO_BA(19, "doba goods add"),
    ALIEXPRESS_IMPORT_GOODS(20, "速卖通手动批量导入"),
    LECANG_IMPORT_GOODS(21, "乐歌同步新增"),

    CREATE_LISTING_TEMPLATE(161, "创建listing商品模板"),
    CREATE_LISTING_FOLLOW_GOODS(162, "商家竞标listing并新增商品"),
    CREATE_CHANCE_GOODS_TEMPLATE(171, "创建机会商品模板"),
    CREATE_SAME_CHANCE_GOODS_FROM_TEMPLATE(172, "根据机会商品模板发布同款商品"),
    SPIDER_GOODS_FROM_1688(181, "从1688爬虫导入"),
    ALI_OPEN(182, "从1688开放平台导入"),
    SPIDER_GOODS_FROM_NIHAO(183, "从nihao爬虫导入"),
    PING_DUO_DUO(184, "从拼多多爬虫导入"),
    TAO_BAO(185, "从淘宝爬虫导入"),
    XI_ZHI_YUE(186, "西之月"),
    GIGA(187, "大健云仓"),

    //todo 1000~1999 商品编辑更新相关
    UPDATE_GOODS(1021, "后台更新商品"),
    ERP_UPDATE_GOODS(1022, "erp更新商品"),
    IMPORT_SKU_PRICE(1023, "批量修改SKU价格"),
    REDUCE_LOCKED_GOODS_PRICE(1024, "锁定商品价格下调"),
    BATCH_UPDATE_STOCK(1025, "批量修改库存"),
    IMPORT_GOODS_FREIGHT(1026, "批量修改国家运费"),
    UPDATE_GOODS_COUNTRY(1027, "更新商品可售区域"),
    UPDATE_SIZE_CHART_TEMPLATE_ID(1028, "尺码表绑定更新"),
    UPDATE_GOODS_CATEGORY(1029, "修改商品类目"),
    UPDATE_PRICE_AND_STOCK(1030, "修改商品价格和库存"),
    UPDATE_GOODS_PROPERTY(1031, "修改商品规格"),
    UPDATE_GOODS_DETAIL(1032, "修改商详"),
    UPDATE_GOODS_FROM_SYNC_LISTING(1033, "所属listing模板修改,同步商品"),
    SCM_UPDATE_GOODS(1034, "scm更新商品"),
    UPDATE_HOLIDAY_SHOP_GOODS_STOCK_UP(1035, "休假店铺备货仓库存变更"),
    SHOP_ACCEPT_REDUCTION(1036, "店铺接受建议降价"),
    UPDATE_DETAIL_FROM_SYNC_LISTING_FOLLOW_GOODS(1037, "listing跟卖商品修改商详，同步listing模板与其他跟卖商品"),
    FRESH_GOODS_FREIGHT(1038, "系统触发，刷新商品国家运费"),
    FRESH_WHOLESALE_GOODS_PRICE(1039, "系统触发，刷新批发商品的价格"),
    FRESH_GOODS_COUNTRY(1040, "系统触发，刷新商品可售国家"),
    UPDATE_COST_PRICE(1041, "自动更新采购成本"),
    REFRESH_GOODS_PRICE_BY_UPDATE_COST_PRICE(1042, "自动更新采购成本，重新刷新商品价格"),
    CANCEL_ORDER_AND_CLEAR_SKU_STOCK(1043, "商家取消订单并清零指定sku库存"),
    TRANSFER_GOODS_TYPE(1044, "转化商品类型"),
    SHOP_ACCEPT_LISTING_REDUCTION(1045, "店铺接受listing建议降价"),
    UPDATE_GOODS_TAG_FOR_NEW_ACTIVITY(1291, "更新标签(flashDeal)"),
    UPDATE_GOODS_TAG_FOR_SPECIAL_ACTIVITY(1292, "更新标签(七日达)"),
    SIMILAR_IMAGE_GOODS_UPDATE(1293, "相似货源更新"),
    UPDATE_GOODS_SHIOP_INFO(1294, "更新店铺信息"),
    ERP_UPDATE_DETAIL(1295, "erp编辑商详"),
    SYNC_FOREIGN_GOODS_STOCK(1297, "同步外来商品池商品库存"),


    //todo 2000~2999 删除状态相关(isDel)
    DELETE_GOODS(2005, "商品删除"),
    REVERT_DELETE_GOODS(2006, "恢复删除商品"),


    //todo 3000~3999 在架状态相关(isShow)
    SHOW(3003, "上下架"),
    TAKE_OFF_BY_NO_STOCK(3004, "无库存下架"),
    AUDITING(3005, "待审核"),
    EXCEL_SHOW(3006, "Excel批量上下架"),
    AUDIT_PASS_AND_SHOW(3030, "去审核通过并上架"),
    ERP_SHOW(3031, "erp上下架"),
    SHOW_BY_CHANCE_GOODS(3032, "机会商品审核通过并上架"),
    TEMP_TD_PROHIBIT(3037, "同盾属性商品检测违规商品禁售"),
    TD_PROHIBIT(3038, "同盾检测违规商品失败"),
    PROHIBIT(3039, "商品禁售"),
    REMOVE_PROHIBIT(3040, "商品解禁"),
    LIMITING(3041, "商品限流"),
    REMOVE_LIMITING(3042, "商品解除限流"),
    TONGDUN_REJECT(3043, "同盾审核驳回"),
    WEIGHTING(3044, "商品限流"),
    REMOVE_WEIGHTING(3045, "商品解除限流"),
    TAKE_OFF_BY_WHOLESALE_NO_GOODS_MOVE(3046, "刷新采购价，无对应move信息，自动下架"),

    SYSTEM_SHOW(3350, "负向业务上下架"),
    SYSTEM_PROHIBIT(3351, "负向业务禁售"),
    SYSTEM_FREEZE(3352, "负向业务冻结"),

    //30 php商品操作上下架
    PHP_SHOW(3301, "php商品下架"),
    PHP_PROHIBIT(3302, "php商品禁售"),

    //31 php店铺操作上下架
    PHP_SHOP_PROHIBIT(3311, "php店铺禁售"),
    PHP_SHOP_VACATION(3312, "php店铺休假"),
    PHP_SHOP_CEASE(3313, "php店铺停用"),


    //todo 4000~4999 锁定状态相关(isLock)
    LOCK(4004, "上锁/解锁"),
    LOCK_BY_SIGN_UP_NEW_ACTIVITY(4041, "上锁（报名flashDeal）"),
    LOCK_BY_SIGN_UP_SPECIAL_ACTIVITY(4042, "上锁（报名七日达）"),
    LOCK_BY_SIGN_UP_PROMOTE(4043, "上锁（报名推广）"),
    LOCK_BY_OPERATION(4441, "加锁（运营加标签）"),
    LOCK_BY_BUSINESS(4442, "加锁（营销加标签）"),
    LOCK_AUTO(4443, "自动加锁"),
    UNLOCK_BY_OPERATION(4444, "解锁（运营解标签）"),
    UNLOCK_BY_BUSINESS(4445, "解锁（营销解标签）"),
    UNLOCK_BY_OPERATION_PASS_APPLY(4446, "解锁（运营通过解锁审核）"),
    UNLOCK_BY_BUSINESS_PASS_APPLY(4447, "解锁（营销通过解锁审核）"),
    UNLOCK_AUTO(4448, "自动解锁"),

    //todo 5000~5999 活动相关
    FLASH_DEAL_BACKUP(5001, "flashDeal价格变更及备份"),
    FLASH_DEAL_RESTORE(5002, "flashDeal价格恢复"),
    JOIN_SPECIAL_ACTIVITY_GOODS_BY_PASS_APPLY(5601, "报名七日达审核通过，加入七日达商品"),
    REMOVE_SPECIAL_ACTIVITY_GOODS_BY_PASS_APPLY(5602, "退出七日达审核通过，退出七日达商品"),
    ;


    /**
     * 操作类型编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    OperationLogTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getMsgByCode(Integer code) {
        for (OperationLogTypeEnums enums : OperationLogTypeEnums.values()) {
            if (enums.code.equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }

    public static Integer getMsgCode(OperationLogTypeEnums operationLogTypeEnums) {
        return operationLogTypeEnums.getCode();
    }

    public static OperationLogTypeEnums mapping(Integer operationLogType) {
        return Stream.of(values()).filter(logType -> Objects.equals(operationLogType, logType.code)).findFirst().orElse(null);
    }

}
