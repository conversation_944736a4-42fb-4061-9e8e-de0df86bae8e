package com.voghion.product.api.output;

import com.colorlight.base.model.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/28 14:28
 * @describe
 */
@Data
@ApiModel
public class CategoryCountryConfigVO extends BaseVO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    /**
     * 国家code
     */
    @ApiModelProperty(value = "国家code")
    private String countryCode;

    /**
     *  0表示未删除 1表示已删除 2 使用中 3 未使用
     */
    @ApiModelProperty(value = "0表示未删除 1表示已删除 2 使用中 3 未使用")
    private Integer status;

    /**
     * 0 所有平台  1 android  2 ios   3 pc，4 h5
     */
    @ApiModelProperty(value = "0 所有平台  1 android  2 ios   3 pc，4 h5")
    private Integer platform;

    /**
     * 最后操作人ID
     */
    @ApiModelProperty(value = "最后操作人ID")
    private Long operUserId;

    /**
     * 最后操作人
     */
    @ApiModelProperty(value = "最后操作人")
    private Long operUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;


    /**
     * 组合的类目信息
     */
    @ApiModelProperty(value = "组合的类目信息")
    private String categoryName;


    /**
     * 类目集合
     */
    @ApiModelProperty(value = "类目集合")
    private List<CategoryVO> categoryList;
}
