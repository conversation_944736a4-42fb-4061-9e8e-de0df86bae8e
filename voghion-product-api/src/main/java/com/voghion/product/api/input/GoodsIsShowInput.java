package com.voghion.product.api.input;

import com.voghion.product.api.enums.OperationLogTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "上下架入参")
public class GoodsIsShowInput implements Serializable {

    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Ids")
    private List<Long> goodsIds;

    /**
     * 上下架标识 只支持0和3
     */
    @ApiModelProperty(value = "上下架 0 下架 1 上架 3禁售 21冻结")
    private String isShow;

    /**
     * 上下架标识
     */
    @ApiModelProperty(value = "商品操作  1,商品 2,店铺")
    private String type;


    /**
     * 店铺id
     */
    private Long shopId;

    private List<Long> shopIds;

    /**
     * 操作类型  0-停用 1-解禁 2-封禁 3休假 4结束休假
     */
    private Integer opt;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private OperationLogTypeEnums operationLogType;
}
