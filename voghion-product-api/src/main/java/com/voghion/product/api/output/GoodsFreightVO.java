package com.voghion.product.api.output;

import com.baomidou.mybatisplus.annotation.TableField;
import com.colorlight.base.model.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2022/4/17 14:00
 * @describe
 */
@Data
@ApiModel
public class GoodsFreightVO extends BaseVO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long goodsId;

    /**
     * skuid
     */
    @ApiModelProperty(value = "skuid")
    private Long skuId;

    /**
     * 商品价格
     */
    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;


    /**
     * 当前运费
     */
    @ApiModelProperty(value = "当前运费")
    private BigDecimal currentFreight;


    /**
     * 当前运费
     */
    @ApiModelProperty(value = "当前运费")
    private BigDecimal freight;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0表示未删除 1表示已删除
     */
    private Integer isDel;
}
