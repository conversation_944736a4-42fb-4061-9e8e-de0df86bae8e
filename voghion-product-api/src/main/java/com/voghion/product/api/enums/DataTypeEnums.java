package com.voghion.product.api.enums;


import org.apache.commons.lang.StringUtils;

/**
 * 数据类型
 *
 * <AUTHOR>
 * @version $Id: DataTypeEnums.java, v 0.1 Feb 7, 2019 10:29:50 PM sky Exp $
 */
public enum DataTypeEnums {

    DATA_TYPE_FRONT("0","0","首页"),
    DATA_TYPE_GOODSID("1","1","商品ID"),                  //商品ID
    DATA_TYPE_URL("2","2","链接"),                        //链接
    DATA_TYPE_FEEDID("3","3","FeedId"),                  //FeedId
    DATA_TYPE_CATEGORYID("4","4","类目ID"),               //类目ID
    DATA_TYPE_PROMOTIONID("5","5","活动ID"),              //活动ID
    DATA_TYPE_CONTENTID("6","6","内容ID"),                //内容ID
    DATA_TYPE_CONTENTLIST("7","7","内容列表"),             //内容列表
    DATA_TYPE_NULL("8","8","不跳转"),                      //不跳转
    DATA_TYPE_CUSTOMCONTENTLIST("9","9","自定义内容列表"), //自定义内容列表
    DATA_TYPE_CUSTOMLIST("13", "13", "自定义商品列表"),    //自定义商品列表
    DATA_TYPE_SECONDPAGE("14","14","首页二级页面") ,       //二级界面
    DATA_TYPE_SECONDPAGE_ICON("15","15","类目Id,moduleItemType表示用于搜索") ,  //类目Id,moduleItemType表示用于搜索
    DATA_TYPE_CUSTOM_SEARCH("16","16","主题页的tag") ,       //二级界面
    DATA_TYPE_CATEGORY_SERACH("17","17","二级类目列表页") ,       //二级界面
    DATA_TYPE_THEME_SEARCH("18","18","主题页") ,                        //主题页
    DATA_TYPE_PRODUCT_SERACH("19","19","公用商品列表用于展示和搜索") ,
    DATA_TYPE_FRONT_TAG("20","20","头部tag"),
    DATA_TYPE_CATEGORY_GOODS_LIST("21","21","类目商品列表") ,
    DATA_TYPE_SECOND_CATEGORY_SERACH("22","22","公用商品列表用于展示和搜索") ,
    DATA_TYPE_SECOND_KILL("24","24","秒杀活动商品类型")



    ;
    /**
     * 前端数据类型
     */
    private final String frontType;
    /**
     * 模块数据类型(feed可配置的数据类型)
     */
    private final String moduleItemType;
    /**
     * 描述
     */
    private final String detail;

    DataTypeEnums(String frontType, String moduleItemType, String detail) {
        this.frontType = frontType;
        this.moduleItemType = moduleItemType;
        this.detail = detail;
    }

    public String getFrontType() {
        return frontType;
    }
    public String getModuleItemType() {
        return moduleItemType;
    }
    public String getDetail() {
        return detail;
    }

    //前台类型转模块数据类型
    public static String front2Module(String frontType){
        if(StringUtils.isNotBlank(frontType)){
            if(DATA_TYPE_GOODSID.frontType.equals(frontType)){
                return DATA_TYPE_GOODSID.moduleItemType;
            }else if(DATA_TYPE_URL.frontType.equals(frontType)){
                return DATA_TYPE_URL.moduleItemType;
            }else if(DATA_TYPE_FEEDID.frontType.equals(frontType)){
                return DATA_TYPE_FEEDID.moduleItemType;
            }else if(DATA_TYPE_CATEGORYID.frontType.equals(frontType)){
                return DATA_TYPE_CATEGORYID.moduleItemType;
            }else if(DATA_TYPE_PROMOTIONID.frontType.equals(frontType)){
                return DATA_TYPE_PROMOTIONID.moduleItemType;
            }else if(DATA_TYPE_CONTENTID.frontType.equals(frontType)){
                return DATA_TYPE_CONTENTID.moduleItemType;
            }else if(DATA_TYPE_CONTENTLIST.frontType.equals(frontType)){
                return DATA_TYPE_CONTENTLIST.moduleItemType;
            }else if(DATA_TYPE_NULL.frontType.equals(frontType)){
                return DATA_TYPE_NULL.moduleItemType;
            }else if(DATA_TYPE_CUSTOMCONTENTLIST.frontType.equals(frontType)){
                return DATA_TYPE_CUSTOMCONTENTLIST.moduleItemType;
            }else if(DATA_TYPE_CUSTOMLIST.frontType.equals(frontType)){
                return DATA_TYPE_CUSTOMLIST.moduleItemType;
            }
        }
        return null;
    }

    //模块数据类型转前台类型
    public static String module2Front(String moduleItemType){
        if(StringUtils.isNotBlank(moduleItemType)){
            if(DATA_TYPE_GOODSID.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_GOODSID.frontType;
            }else if(DATA_TYPE_URL.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_URL.frontType;
            }else if(DATA_TYPE_FEEDID.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_FEEDID.frontType;
            }else if(DATA_TYPE_CATEGORYID.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_CATEGORYID.frontType;
            }else if(DATA_TYPE_PROMOTIONID.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_PROMOTIONID.frontType;
            }else if(DATA_TYPE_CONTENTID.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_CONTENTID.frontType;
            }else if(DATA_TYPE_CONTENTLIST.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_CONTENTLIST.frontType;
            }else if(DATA_TYPE_NULL.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_NULL.frontType;
            }else if(DATA_TYPE_CUSTOMCONTENTLIST.moduleItemType.equals(moduleItemType)){
                return DATA_TYPE_CUSTOMCONTENTLIST.frontType;
            }else if(DATA_TYPE_CUSTOMLIST.frontType.equals(moduleItemType)){
                return DATA_TYPE_CUSTOMLIST.moduleItemType;
            }
        }
        return null;
    }

}
