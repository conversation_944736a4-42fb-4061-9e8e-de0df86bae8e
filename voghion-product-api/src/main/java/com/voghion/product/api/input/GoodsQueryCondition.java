package com.voghion.product.api.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @date: 2022/9/22 下午5:41
 * @author: jashley
 */
@Data
public class GoodsQueryCondition implements Serializable {
    private List<Long> goodsIds;
    private List<Long> noGoodsIds;
    private String name;
    /**
     * 查询类目和子类目
     */
    private Long categoryId;
    private Integer isShow;
    private Long shopId;
    private BigDecimal startMinPrice;
    private BigDecimal endMinPrice;
    private BigDecimal startMaxPrice;
    private BigDecimal endMaxPrice;
    private Integer pageNow;
    private Integer pageSize;
}
