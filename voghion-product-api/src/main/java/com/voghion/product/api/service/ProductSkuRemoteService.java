package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.output.GoodInfoOutput;
import com.voghion.product.api.output.GoodsItemDTO;
import com.voghion.product.api.output.GoodsItemOutput;

import java.util.List;

/**
 * 根据goodsId查询SKU信息
 *
 * <AUTHOR> chenxuan
 * @date 2021/03/28 17:45
 **/
public interface ProductSkuRemoteService {

    /**
     * 根据goodsId查询所有的SKU信息
     * @param goodsId
     * @return
     */
    Result<List<GoodsItemDTO>> queryListGoodsItemByGoodsId(Long goodsId);

    /**
     * 根据skuId 查询商品的sku信息
     * @param skuIds
     * @return
     */
    Result<List<GoodsItemDTO>> queryListGoodsItemBySkuIds(List<Long> skuIds);


    /**
     * 根据goodsIds 查询对应的商品信息及其SKU信息
     * @param goodsIds
     * @return
     */
    Result<List<GoodInfoOutput>>  queryListGoodsInfoAndSkuByGoodsIds(List<Long> goodsIds);

}
