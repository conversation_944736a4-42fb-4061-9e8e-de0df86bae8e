package com.voghion.product.api.output;

import com.colorlight.base.model.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品对象，只有商品没有sku
 * <AUTHOR>
 * @version $Id: GoodsVO.java, v 0.1 Feb 20, 2019 9:41:55 PM sky Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsOnlyDTO extends BaseDTO {
    private static final long serialVersionUID = -132009925764017832L;
    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品类型：1表示单品 2表示组合商品
     */
    private Byte type;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 区域ID，限制商品的销售地域，为null时表示不受限制
     */
    private String areaCode;


    /**
     * 类目id
     */
    private Long categoryId;
    
    /**
     * 是否上架：1-上架；0-下架
     */
    private String isShow;
    
    /**
     * 展示价格（用于列表页的价格展示）
     */
    private BigDecimal marketPrice; 
    
    /**
     * 销售价格（用于列表页的价格展示）
     */
    private BigDecimal price;

    /**
     * 团购价格
     */
    private BigDecimal grouponPrice;

    /**
     * 展示价格（用于列表页的价格展示）
     */
    private String marketPriceStr; 
    
    /**
     * 销售价格（用于列表页的价格展示）
     */
    private String priceStr; 
    
    /**
     * 商品图片
     */
    private List<String> imgs;
    
    /**
     * 状态：1表示正常 99表示禁用
     */
    private Byte status;

    /**
     * 是否删除：0表示未删除 1表示已删除
     */
    private Byte isDel;
    
    /**
     * 商品主图
     */
    private String mainImage;
    /**
     * 商品url
     */
    private String goodsURL;

    /**
     * 商品销量
     */
    private Long sales;

    /**
     * 评论数
     */
    private Integer commentNumber;

    /**
     * 评分数
     */
    private Double score;

    /**
     * 生产周期
     */
    private Integer leadTime;


    /**
     * 重量
     */
    private String weight;

    /**
     * 体积
     */
    private String packageSize;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 折扣标签
     */
    private String discountLabel;

    /**
     * 店铺名称
     */
    private String  shopName;

    /**
     * 店铺url
     */
    private String shopUrl;

    private Long shopId;

    /**
     * 标签
     */
    private String tag;


}
