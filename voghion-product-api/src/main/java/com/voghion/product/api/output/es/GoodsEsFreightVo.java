package com.voghion.product.api.output.es;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @date: 2022/3/10 16:47
 * @author: jashley
 */
@Data
public class GoodsEsFreightVo implements Serializable {
    private static final long serialVersionUID = -3856270687625753183L;
    /**
     * 操作类型 0新增 1修改 2删除
     */
    private Long opt;
    private String code;
    private String updateTime;
    private Long goodsId;
    private BigDecimal price;
    private String createTime;
    private BigDecimal currentFreight;
    private Long skuId;
}
