package com.voghion.product.api.output;


import com.colorlight.base.model.PageView;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryGoodsOrderDTO extends PageView {

    /**
     * 最低价
     */
    private BigDecimal startPrice;


    /**
     * 商品Id列表
     */
    private List<Long> goodIds;


    /**
     * 前台类目Id   //后台类目把
     */
    private Long categoryId;


    /**
     * 后台类目列表
     */
    private List<Long> categoryIds;

    /**
     * 是否过滤无效的数据
     */
    private Boolean isVaild;


    /**
     * 虚拟商品列表Id
     */
    private Long customId;

    /**
     * 置顶的集合id
     */
    private Long cId;

    /**
     * 最高价
     */
    private BigDecimal endPrice;


    /**
     * 国家
     */
    private String country;


    /**
     * app的渠道
     */
    private String appChannel;


    /**
     * 排序类型（1，价格由低到高，2价格由高到底，3，销量由低到高排序，4销量由高到低排序,
     * 5 新款排序,6评分由高到低, 7热度由高到底--暂时与默认相同，8 默认排序，以原始销量进行排序，由高到低）
     */
    private Integer orderBy;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺名称
     */
    private Long shopId;

    public Boolean getVaild() {
        return isVaild;
    }

    public void setVaild(Boolean vaild) {
        isVaild = vaild;
    }
}
