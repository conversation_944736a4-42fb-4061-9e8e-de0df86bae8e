package com.voghion.product.api.service;


import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.input.FilterItemInput;
import com.voghion.product.api.input.PropertyQueryDTO;
import com.voghion.product.api.output.FilterItemsVO;
import com.voghion.product.api.output.PropertyDTO;
import com.voghion.product.api.output.PropertyValueDTO;
import com.voghion.product.model.dto.PropertyTemplateDto;
import com.voghion.product.model.query.PropertyTemplateQuery;
import com.voghion.product.model.vo.PropertyDetailInfoVO;
import com.voghion.product.model.vo.PropertyStandardInfoVO;
import com.voghion.product.model.vo.PropertyTemplateDetailVO;
import com.voghion.product.model.vo.PropertyTemplateVO;

import java.util.List;

/**
 * author archer
 * createOn 2021/4/23 16:09
 */
public interface PropertyRemoteService {


    /**
     * 查询属性信息
     * @param propertyIdList
     * @return
     */
    Result<List<PropertyDTO>> queryPropertyByIds(List<Long> propertyIdList);


    /**
     * 查询属性值信息
     * @param propertyIdsList
     * @return
     */
    Result<List<PropertyValueDTO>> queryPropertyValueDTOByIds(List<Long> propertyIdsList);


    /**
     * 一次性查询出对应的属性和属性值以及属性图片
     * @param queryDTO
     * @return
     */
    Result<List<PropertyDTO>> queryPropertyByQuery(PropertyQueryDTO queryDTO);

    //添加/更新商品属性
    Result<Boolean> saveOrUpdatePropertyStandardInfo(PropertyStandardInfoVO propertyStandardInfoVO);

    //查询商品属性
    Result<PageView<PropertyStandardInfoVO>> queryPropertyStandardInfo(PropertyStandardInfoVO propertyStandardInfoVO);

    //删除商品属性
    Result<Boolean> removePropertyStandardInfo(Long id);

    //查询商品属性详情
    Result<PropertyStandardInfoVO> queryPropertyInfoByStandId(Long id);

    //删除商品详情
    Result<Boolean> removePropertyDetailInfo(Long id);

    Result<List<PropertyDetailInfoVO>> queryByCategoryId(Long categoryId);

    Result<List<PropertyTemplateDto>> queryPropertyTemplateListByIds(List<Long> templateIds);

    Result<List<PropertyTemplateDetailVO>> queryPropertyTemplateDetailListByIds(List<Long> templateIds);

    Result<List<PropertyDetailInfoVO>> queryByIds(List<Long> detailIds);

    Result<List<PropertyStandardInfoVO>> queryPropertyInfoByCondition(PropertyStandardInfoVO propertyStandardInfoVO);

    Result<List<FilterItemsVO>> queryFilterItems(FilterItemInput input);

    PageView<PropertyTemplateVO> templateList(PropertyTemplateQuery query);

    PropertyTemplateVO templateDetail(Long id);
}
