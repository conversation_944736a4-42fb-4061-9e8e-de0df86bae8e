//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output.es;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class GoodsEsGoodsVo implements Serializable {
    /**
     * 商品id
     */
    private String goodsId;
    /**
     * 是否删除
     */
    private Integer isDel;
    /**
     * 上下架
     */
    private Integer isShow;
    /**
     * 操作类型 0新增 1修改 2删除
     */
    private Integer opt;
    /**
     * 修改时间
     */
    private String updateTime;

}
