package com.voghion.product.api.output;

import com.voghion.product.model.po.FrontCategory;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2022/2/14 14:20
 * @describe
 */
@Data
public class FrontCategoryInfoVO implements Serializable {

    private static final long serialVersionUID = -1515278637445610447L;

    //需要查询的 前台类目id
    private List<Long> ids;

    //类目本身
    private List<FrontCategory> frontCategory;

    //次一级类目信息
    private List<FrontCategory> frontCategories;

    //前台类目的所有子类目 包含自身
    private List<Long> frontCategoryIds;

    //所有前台类目对应的所有后台类目
    private List<Long> categoryIds;

    /**
     * app系统，ios或者android
     */
    private String appSystem;

    /**
     * app版本
     */
    private String appVersion;

    private Integer status;

}
