package com.voghion.product.api.input;

import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2022/8/31 16:36
 * @describe
 */
@Data
@ApiModel
public class SkuLimitPriceInput extends PageParam {


    /**
     * 类目id
     */
    @ApiModelProperty(value = "类目id")
    private Long categoryId;


    /**
     * 状态1 生效 2 失效
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
}
