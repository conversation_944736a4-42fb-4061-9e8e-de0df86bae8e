package com.voghion.product.api.input;

import com.voghion.product.api.dto.*;
import com.voghion.product.api.dto.size.PackageSizeRemoteDto;
import com.voghion.product.api.dto.size.SizeChartTemplateRemoterDto;
import com.voghion.product.api.dto.size.SizeTableRemoteDto;
import com.voghion.product.model.vo.GoodsManualVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 14:51
 */
@Data
public class ItemGoodsInfoRemoteInputDto implements Serializable {

    private Long id;

    private Long categoryId;

    private Long brandId;

    private String itemCode;

    private String name;

    private String originalName;

    private String description;

    private Long sortValue;

    private Integer type;

    private String isShow;

    private String weight;

    private PackageSizeRemoteDto packageSize;

    private List<SpecsRemoteDto> specs;

    private String mainImage;

    private List<String> detailsImgs;

    private List<String> goodsImages;

    private List<String> goodsVideos;

    private List<String> realShotImgs;

    private List<String> country;

    private List<ItemGoodsPropertyRemoteDto> properties;

    @ApiModelProperty(value = "app渠道")
    private Integer appChannel;

    @ApiModelProperty(value = "来源渠道")
    private Integer channel;

    private Long sizeChartTemplateId;

    private List<SizeTableRemoteDto> sizeTables;

    private List<SizeTableRemoteDto> originalSizeTables;

    private List<PropertyGoodsInfoRemoteDto> propertyGoodsInfoVOS;

    private List<PropertyDetailInfoRemoteDto> propertyDetailInfoVOList;

    private List<GoodsManualRemoteDto> goodsManualVOList;

    @ApiModelProperty("说明视频")
    private List<GoodsManualVO> goodsManualVideoList;

    private Integer operation = 0;

    private Integer logisticsProperty;

    private Boolean IsDefaultDelivery;

    private List<GoodsFreightRemoteDto> freightList;

    private List<ItemGoodsSkuInfoRemoteDto> skuInfo;

    private Integer useType = 0;

    private ItemGoodsFillRemoteDto itemGoodsFill;

    private ItemGoodsExtDetailFillRemoteDto goodsExtDetailFill;

    private Long sales;

    private List<GoodsExtraPropertyRemoteDto> goodsExtraPropertyVOS;

    private Boolean isReductionPrice;

    private String arrival;

    private Long draftId;

    private Long shopId;

    private String storeName;

    private String buyerClientinfo;

    /**
     * 操作日志类型
     * <br/>
     * see com.voghion.product.api.enums.OperationLogTypeEnums
     */
    private Integer operationLogType;

    private boolean needCheckShop = true;

    private Integer sourceType = 0;

    private String sizeImage;

    private SizeChartTemplateRemoterDto sizeChartTemplateVo;

    private Boolean isTask;

}
