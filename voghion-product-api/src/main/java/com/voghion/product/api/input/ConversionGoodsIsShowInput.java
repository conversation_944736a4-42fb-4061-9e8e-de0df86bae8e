package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "上下架入参")
public class ConversionGoodsIsShowInput implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Ids")
    private List<Long> goodsIds;

    /**
     * 操作类型  1。禁售 2.解禁 3.冻结 4.解冻
     */
    private Integer optType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 理由
     */
    private String reason;
}
