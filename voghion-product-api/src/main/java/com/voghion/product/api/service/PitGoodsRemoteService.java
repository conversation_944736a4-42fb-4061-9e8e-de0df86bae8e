package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;

/**
 * 坑位商品接口
 *
 * <AUTHOR>
 * @date 2021/06/08 16:12
 **/
public interface PitGoodsRemoteService {

    /**
     * 定时任务 -- 将待测试商品 处理为 ---正在测试商品
     */
    Result<Boolean> doTaskPitGoodsInTest();

    /**
     * 定时任务 -- 将正在测试的商品  处理为  已完成/待测试
     */
    Result<Boolean>  doTaskPitGoodsBeenCompleted();




}
