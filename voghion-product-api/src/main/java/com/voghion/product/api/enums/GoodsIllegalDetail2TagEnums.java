package com.voghion.product.api.enums;

import lombok.Getter;

@Getter
public enum GoodsIllegalDetail2TagEnums {

    TAG_470_1("[图像]合规私有标签/voghion/马赛克", 470L),
    TAG_470_2("[图像]合规私有标签/voghion/图片疑似含网址", 470L),
    TAG_472_1("[图像]禁销/安全隐患类", 472L),
    TAG_461_1("[图像]禁销/电子类违禁产品/激光设备", 461L),
    TAG_466_1("[图像]禁销/电子类违禁产品/监视窃听", 466L),
    TAG_471_1("[图像]禁销/动植物相关", 471L),
    TAG_462_1("[图像]禁销/毒品/大麻", 462L),
    TAG_462_2("[图像]禁销/化妆品", 462L),
    TAG_465_1("[图像]禁销/色情低俗", 465L),
    TAG_465_2("[图像]禁销/色情低俗/情趣内衣", 465L),
    TAG_465_3("[图像]禁销/色情低俗/情趣内衣无真人", 465L),
    TAG_465_4("[图像]禁销/色情低俗/色情图片", 465L),
    TAG_465_5("[图像]禁销/色情低俗/情趣用品", 465L),
    TAG_465_6("[图像]禁销/色情低俗/性感图片", 465L),
    TAG_465_7("[图像]禁销/商品低俗", 465L),
    TAG_465_8("[图像]禁销/商品低俗/中指", 465L),
    TAG_468_1("[图像]禁销/收藏类", 468L),
    TAG_462_3("[图像]禁销/危险化学品类", 462L),
    TAG_469_1("[图像]禁销/烟具", 469L),
    TAG_464_1("[图像]禁销/药品", 464L),
    TAG_464_2("[图像]禁销/药品/药", 464L),
    TAG_464_3("[图像]禁销/医疗器械", 464L),
    TAG_462_4("[图像]禁销/易燃易爆品", 462L),
    TAG_463_1("[图像]禁销/宗教/酒瓶", 463L),
    TAG_463_2("[图像]禁销/宗教/上帝相关", 463L),
    TAG_463_3("[图像]禁销/宗教/圣诞老人", 463L),
    TAG_463_4("[图像]禁销/宗教/圣诞树", 463L),
    TAG_469_2("[图像]禁销/宗教/香烟", 469L),
    TAG_463_5("[图像]涉政", 463L),
    TAG_463_6("[图像]涉政/涉政asr", 463L),
    TAG_461_2("[图像]违禁/管制刀具", 461L),
    TAG_461_3("[图像]违禁/军警用品", 461L),
    TAG_461_4("[图像]违禁/枪支弹药/枪支", 461L),
    TAG_465_9("[文本][图像]禁销/色情低俗/色情图片", 465L),
    TAG_472_2("[文本]禁销/安全隐患类", 472L),
    TAG_465_10("[文本]禁销/色情低俗", 465L),
    TAG_465_11("[文本]禁销/色情低俗/情趣内衣", 465L),
    TAG_465_12("[文本]禁销/色情低俗/情趣用品", 465L),
    TAG_465_13("[文本]禁销/色情低俗/色情描述", 465L),
    TAG_469_3("[文本]禁销/吸烟相关", 469L),
    TAG_464_4("[文本]禁销/药品", 464L),
    TAG_464_5("[文本]禁销/医疗器械", 464L),
    TAG_462_5("[文本]禁销/易燃易爆品", 462L),
    TAG_465_14("[文本]色情", 465L),
    TAG_465_15("[文本]色情/性相关用品/情趣用品", 465L),
    ;
    private String desc;
    private Long tagId;

    private GoodsIllegalDetail2TagEnums(String desc, Long tagId) {
        this.desc = desc;
        this.tagId = tagId;
    }

    public static long getTagIdByDesc(String desc) {
        for (GoodsIllegalDetail2TagEnums value : GoodsIllegalDetail2TagEnums.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getTagId();
            }
        }
        return 0L;
    }

}
