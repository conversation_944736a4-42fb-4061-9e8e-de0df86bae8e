package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2022/8/31 16:46
 * @describe
 */
@Data
@ApiModel
public class SkuLimitPriceVO implements Serializable {
    @ApiModelProperty(value = "更新的时候必传Id" ,required = false)
    private Long id;

    /**
     * 类目id
     */
    @ApiModelProperty(value = "类目id" ,required = true)
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目级别
     */
    private Integer level;

    /**
     * 价差比例max/min
     */
    @ApiModelProperty(value = "价差比例" ,required = true)
    private BigDecimal priceRatio;

    /**
     * 价格差值max-min
     */
    @ApiModelProperty(value = "价格差值" ,required = true)
    private BigDecimal priceDiff;

    /**
     * 类目最高价格
     */
    @ApiModelProperty(value = "类目最高价格")
    private BigDecimal maxCategoryPrice;

    /**
     * 状态1 生效 2 失效
     */
    @ApiModelProperty(value = "状态1 生效 2 失效" ,required = true)
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String operatorTime;



    /**
     * 创建时间
     */
    private String createTime;

    /**
     * ids
     */
    @ApiModelProperty(value = "批量单个更新的时候传入Ids 单个也可以传id" ,required = false)
    private  List<Long> ids;

}
