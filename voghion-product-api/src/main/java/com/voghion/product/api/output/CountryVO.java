package com.voghion.product.api.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * <p>
 * 国家信息
 */
@Data
@ApiModel
public class CountryVO implements Serializable {
	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "id")
	private Long id;

	/**
	 * 国家代码
	 */
	@ApiModelProperty(value = "国家code")
	private String code;

	/**
	 * 国家代码（3位）
	 */
	@ApiModelProperty(value = "国家名称")
	private String name;

	/**
	 * 国家英文名
	 */
	@ApiModelProperty(value = "国家短名称")
	private String shortName;

	/**
	 * 国家中文名
	 */
	private String description;

	/**
	 * 排序越小的排前面
	 */
	private Integer sort;
}