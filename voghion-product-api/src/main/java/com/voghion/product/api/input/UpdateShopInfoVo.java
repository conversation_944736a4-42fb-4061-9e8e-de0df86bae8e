package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class UpdateShopInfoVo implements Serializable {
    private static final long serialVersionUID = -8841128195533446L;

    @ApiModelProperty(value = "商品id", required = true)
    private Long goodsId;

    @ApiModelProperty(value = "店铺id", required = true)
    private Long shopId;

    @ApiModelProperty(value = "店铺名称", required = true)
    private String shopName;

    @ApiModelProperty(value = "尺码表模板id")
    private Long sizeChartTemplateId;

    @ApiModelProperty(value = "更新人", required = true)
    private String updateBy;

}