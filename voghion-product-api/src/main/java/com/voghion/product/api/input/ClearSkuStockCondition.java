package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ClearSkuStockCondition implements Serializable {
    private static final long serialVersionUID = -8848237628195533446L;

    @ApiModelProperty(value = "商品id", required = true)
    private Long goodsId;

    @ApiModelProperty(value = "规格信息", required = true)
    private Long skuId;

}
