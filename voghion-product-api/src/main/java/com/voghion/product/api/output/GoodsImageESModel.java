//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import java.io.Serializable;
import java.time.LocalDateTime;

public class GoodsImageESModel implements Serializable {
    private Long id;
    private Long goodsId;
    private Long skuId;
    private Integer imageType;
    private String url;
    private LocalDateTime createTime;
    private String kw;
    private String originalUrl;
    private Integer transported;

    public GoodsImageESModel() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsId() {
        return this.goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Long getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getImageType() {
        return this.imageType;
    }

    public void setImageType(Integer imageType) {
        this.imageType = imageType;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getKw() {
        return this.kw;
    }

    public void setKw(String kw) {
        this.kw = kw;
    }

    public String getOriginalUrl() {
        return this.originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    public Integer getTransported() {
        return this.transported;
    }

    public void setTransported(Integer transported) {
        this.transported = transported;
    }
}
