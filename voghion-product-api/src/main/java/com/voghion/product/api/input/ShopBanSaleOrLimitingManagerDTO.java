package com.voghion.product.api.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ShopBanSaleOrLimitingManagerDTO implements Serializable {

    private static final long serialVersionUID = -117129338025337877L;
    /**
     * 必传，操作类型：1-新增限流 2-新增禁售 3-解除限流 4-解除禁售
     */
    @ApiModelProperty("必传，操作类型：1-新增限流 2-新增禁售 3-解除限流 4-解除禁售")
    @NotNull(message = "操作类型不能为空")
    @Range(min = 1, max = 4, message = "操作类型必须在1到4之间")
    private Integer optType;

    /**
     * 必传，店铺ID，多个用换行分隔
     */
    @ApiModelProperty("必传，店铺ID，多个用换行分隔，单次不能超过100个")
    @NotBlank(message = "商家ID不能为空")
    private String shopIds;

    /**
     * 限流比例 0:不限流
     */
    @ApiModelProperty("限流比例 0:不限流")
    private Integer limitingGear;

    /**
     * 封禁/限流类型 1天数 2 永久
     */
    @ApiModelProperty("封禁/限流类型 1:天数 2:永久")
    private Integer type;

    /**
     * 天数
     */
    @ApiModelProperty("天数")
    private Integer days;

    /**
     * 封禁/限流原因
     */
    @ApiModelProperty("封禁/限流原因")
    private String reason;


}
