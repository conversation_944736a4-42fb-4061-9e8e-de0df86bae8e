package com.voghion.product.api.input;

import com.voghion.product.api.dto.GoodsUpdateShopInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class ChangeShopInfoVo implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "商品变更店铺集合", required = true)
    private List<GoodsUpdateShopInfoVO> updateShopInfoVOS;

    @ApiModelProperty(value = "更新人", required = true)
    private String updateBy;

}