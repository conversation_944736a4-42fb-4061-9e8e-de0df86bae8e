package com.voghion.product.api.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodsItemUpdateInput implements Serializable {

    private static final long serialVersionUID = 8937400883765295351L;
    private List<GoodsItemDTO> itemDTOS;
    @ApiModelProperty(value = "更新范围 1价格 2库存 3价格+库存")
    private Integer updateScope;
}
