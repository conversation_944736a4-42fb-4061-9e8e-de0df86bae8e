package com.voghion.product.api.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProhibitionTaskDto implements Serializable {
    private static final long serialVersionUID = -5670094972387333359L;

    /**
     * 任务类型 1 解封 2 封禁
     */
    private Integer taskType;

    /**
     * 是否执行成功标识 0否 1是
     */
    private Integer status;

    private Integer requestCount;

    private Integer limits;


}
