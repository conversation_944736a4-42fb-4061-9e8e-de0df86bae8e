package com.voghion.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CalculateWholesaleFreightDto implements Serializable {
    private static final long serialVersionUID = 591223985630527436L;

    @ApiModelProperty("商品重量")
    private BigDecimal weight;

    @ApiModelProperty("物流属性")
    private Integer logisticsProperty;

    @ApiModelProperty("国家")
    private String country;

    @ApiModelProperty("物流场景 1、通用 2、尾货 3、含40/45/50负向标")
    private Integer logisticsScene;
}
