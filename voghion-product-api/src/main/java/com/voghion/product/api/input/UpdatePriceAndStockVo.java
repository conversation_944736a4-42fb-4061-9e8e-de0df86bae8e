package com.voghion.product.api.input;

import com.voghion.product.model.vo.GoodsFreightVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class UpdatePriceAndStockVo implements Serializable {
    private static final long serialVersionUID = -8848237628195533446L;

    @ApiModelProperty(value = "商品id", required = true)
    private Long goodsId;

    @ApiModelProperty("上下架 1上架 0下架")
    private Integer isShow;

    @ApiModelProperty(value = "sku价格、库存信息", required = true)
    private List<SkuUpdateDto> skuUpdateList;

    @ApiModelProperty(value = "物流属性", required = true)
    private Integer logisticsProperty;

    @ApiModelProperty(value = "重量", required = true)
    private Double weight;

    @ApiModelProperty("申请理由")
    private String applyReason;

    @ApiModelProperty("国家运费")
    private List<GoodsFreightVO> freightList;

    private Long shopId;

    /**
     * 跳过审批 1是0否
     */
    private Integer skipAudit = 0;

    private Integer channel = 0;

    @ApiModelProperty("是否接受降价推送")
    private Integer acceptReductionPush;

    @Data
    public static class SkuUpdateDto implements Serializable {
        private static final long serialVersionUID = 7292029506017851639L;
        @ApiModelProperty(value = "goodsItem主键id", required = true)
        private Long goodsItemId;

        @ApiModelProperty(value = "sku价格", required = true)
        private BigDecimal originalPrice;

        @ApiModelProperty(value = "sku采购价")
        private BigDecimal costPrice;

        @ApiModelProperty(value = "sku库存", required = true)
        private Long stock;

        @ApiModelProperty(value = "sku状态 0下架 1上架", required = true)
        private Integer skuStatus;

        @ApiModelProperty(value = "重量")
        private Double weight;

        @ApiModelProperty(value = "默认运费")
        private BigDecimal defaultDelivery;
    }
}
