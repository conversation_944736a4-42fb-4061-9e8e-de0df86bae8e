package com.voghion.product.api.output;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品领域模型
 * <AUTHOR>
 */
@Data
@ApiModel(value = "商品模型")
public class GoodsDataOutput  extends ContentBaseVO{

    private static final long serialVersionUID = 7656059174614268478L;
    private Long id;

    /**
     * 商品Id:skuId
     */
    @ApiModelProperty(value = "商品Id")
    private String goodsId;

    /**
     * 只用于查询goodsId
     */
    @ApiModelProperty(value = "只用于查询goodsId")
    private String onlyGoodsId;

    /**
     * 数据类型（商品）
     */
    @ApiModelProperty(value = "数据类型")
    private String type;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * sku 名称
     */
    @ApiModelProperty(value = "sku名称")
    private String skuName;

    /**
     *
     * 商品销售价格
     */
    @ApiModelProperty(value = "销售价")
    private BigDecimal price;

    /**
     *
     * 商品展示价格
     */
    @ApiModelProperty(value = "划线价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "团购价")
    private BigDecimal grouponPrice;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片地址")
    private String imgUrl;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String goodsDesc;

    /**
     * 库存
     */
    @ApiModelProperty(value = "库存")
    private Integer stock;

    /**
     * 是否是有效商品
     */
    @ApiModelProperty(value = "是否有效")
    private boolean isValid;

    /**
     * 是否团购商品
     */
    private boolean isGroupon;



    /**
     * 商品销量
     */
    @ApiModelProperty(value = "销量")
    private Long sales;

    @ApiModelProperty(value = "销量")
    private Long sold;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private Double score;

    /**
     * 评论数
     */
    @ApiModelProperty(value = "评论")
    private Integer commentNumber;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺Id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名")
    private String shopName;

    @ApiModelProperty(value = "折扣标签")
    private String discountLabel;



    /**
     * 是否收藏商品
     */
    private Boolean wishGoods = false;

    @ApiModelProperty("商品创建时间")
    private Date createTime;

    public void setValid(boolean isValid) {
        this.isValid = isValid;
    }

    public String getGoodsId() {
        return goodsId;
    }


    @ApiModelProperty(value = "视频地址")
    private String videoUrl;
    /**
     * 仅获取goodsId不获取SKU
     * @return
     */
    public String getOnlyGoodsId(){
        if(goodsId.indexOf(":")!=-1){
            String[] goods=  goodsId.split(":");
            return goods[0];
        }
        return goodsId;
    }

    public void setIsValid(boolean isValid){
        this.isValid = isValid;
    }
}
