//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import com.colorlight.base.utils.DateUtil;
import com.voghion.product.inner.dto.GoodsItemLogDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class GoodsOperateLogVo implements Serializable {
    private static final long serialVersionUID = -3339464441550506599L;
    private Integer opt;
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 商家skuId
     */
    private Long skuId;
    /**
     * 规格(SKU名称)
     */
    private String skuName;
//    /**
//     * 上下架
//     */
//    private Integer isShow;
    /**
     * 修改账号
     */
    private String operator;
    /**
     * 修改时间
     */
    private Date updateTimeDate;
    private String updateTime;
//    private Integer isDel;

    private UpdateInfo newGoods;
    private UpdateInfo oldGoods;


    public GoodsOperateLogVo(GoodsItemLogDto newGoods) {
        this.opt = newGoods.getOpt();
        this.goodsId = newGoods.getGoodsId();
        this.skuId = newGoods.getSkuId();
        this.skuName = newGoods.getName();
//        this.isShow = newGoods.getIsShow();
        this.updateTimeDate = newGoods.getUpdateTime();
        this.updateTime = DateUtil.formatDate(newGoods.getUpdateTime(),DateUtil.newFormat);
        this.operator = newGoods.getUpdateBy();
        this.oldGoods = new UpdateInfo();
        this.newGoods = new UpdateInfo(newGoods);
    }

    public GoodsOperateLogVo(GoodsItemLogDto oldGoods, GoodsItemLogDto newGoods) {
        this.opt = newGoods.getOpt();
        this.goodsId = newGoods.getGoodsId();
        this.skuId = newGoods.getSkuId();
        this.skuName = newGoods.getName();
        this.updateTimeDate = newGoods.getUpdateTime();
        this.updateTime = DateUtil.formatDate(newGoods.getUpdateTime(),DateUtil.newFormat);
        this.operator = newGoods.getUpdateBy();
        this.oldGoods = new UpdateInfo(oldGoods);
        this.newGoods = new UpdateInfo(newGoods);
    }


    @Data
    @NoArgsConstructor
    public static class UpdateInfo implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        /**
         * 默认运费
         */
        private BigDecimal freight;
        /**
         * 价格
         */
        private BigDecimal price;
        private Long skuId;
        private String skuName;
        private BigDecimal grouponPrice;
        /**
         * 库存
         */
        private Integer stock;
        private Integer isShow;
//        private Date createTime;
//        private Date updateTime;

        public UpdateInfo(GoodsItemLogDto vo) {
            this.freight = vo.getDefaultDelivery();
            this.price = vo.getOrginalPrice();
            this.grouponPrice = vo.getOriginalGrouponPrice();
            this.skuId = vo.getSkuId();
            this.skuName = vo.getName();
            this.stock = vo.getStock() == null ? null : Math.toIntExact(vo.getStock());
            this.isShow = vo.getIsShow();
//            this.createTime = vo.getCreateTime();
//            this.updateTime = vo.getUpdateTime();
        }
    }


}
