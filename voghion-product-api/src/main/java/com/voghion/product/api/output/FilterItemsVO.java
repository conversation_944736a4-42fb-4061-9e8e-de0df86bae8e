package com.voghion.product.api.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class FilterItemsVO implements Serializable {

    private static final long serialVersionUID = 6052663975425687574L;
    @ApiModelProperty(value = "filter模块类型")
    private Integer type;
    @ApiModelProperty(value = "filter模块展示文案")
    private String showVal;
    @ApiModelProperty(value = "filter模块展示文案-原始文案")
    private String showValOriginal;
    @ApiModelProperty(value = "filter模块明细列表")
    private List<FilterItemsDetailVO> detailList;

    @Data
    public static class FilterItemsDetailVO implements Serializable {
        private static final long serialVersionUID = 1648131216677241381L;
        @ApiModelProperty(value = "展示文案")
        private String content;
        @ApiModelProperty(value = "展示文案-原始文案")
        private String contentOriginal;
        @ApiModelProperty(value = "展示url")
        private String url;
        @ApiModelProperty(value = "内部关联id")
        private Long innerId;
        @ApiModelProperty(value = "内部展示类型 0 文案; 1 url; 2 文案+url")
        private Integer innerType;
    }
}
