package com.voghion.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class CalculateWholesaleFreightResultDto implements Serializable {
    private static final long serialVersionUID = 591223985630527436L;

    @ApiModelProperty("总运费")
    private BigDecimal totalAmount;

    @ApiModelProperty("各sku运费")
    private Map<Long, BigDecimal> skuAmountMap;

}
