package com.voghion.product.api.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @date: 2022/8/8 下午4:09
 * @author: jashley
 */
@Data
public class ShopPromoteGoodsQueryVo implements Serializable {
    private static final long serialVersionUID = -865326535120885949L;
    private List<Long> goodsIdList;

    private Long shopId;

    private String goodsName;

    private Long categoryId;

    private Integer isShow;


    private BigDecimal startPrice;

    private BigDecimal endPrice;

    private Integer pageNow = 1;
    private Integer pageSize = 20;
}
