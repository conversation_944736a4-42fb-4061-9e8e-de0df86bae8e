package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class GoodsItemESModel implements Serializable {


    private Long id;

    //商品ID
    private Long goodsId;

    //SKU ID
    private Long skuId;

    //价格
    private BigDecimal price;

    //市场价
    private BigDecimal marketPrice;

    //原始价格
    private BigDecimal orginalPrice;

    //原划线价
    private BigDecimal orginalMarketPrice;

    //SKU数量
    private Integer num;

    //渠道标识，暂未定值
    private Integer channel;

    //创建时间
    private LocalDateTime createTime;

    //商品sku价格系数/%
    private Integer ratio;

    //成本价
    private BigDecimal costPrice;

    //以分号分割一对值，冒号组合属性名和属性值，例如:1001:31009;1002:31009
    private String pvalueStr;

    //属性名对应的属性值组合成的属性描述，例如:欧莱雅修复系列泡沫洗面奶200ml
    private String pvalueDesc;

    //库存数量
    private Integer stock;

    //SKU名称
    private String name;

    //第三方sku_id
    private String originalSkuId;

    //销售量
    private Long sold;

    private String skuImage;

    //重量字段
    private String weight;

    //体积
    private String packageSize;

    private BigDecimal defaultDelivery;

    /**
     * 最小购买数量
     */
    private Integer minPurchaseQuantity;

    /**
     * 锁定中库存数量
     */
    private Integer lockStock;

    /**
     * sku状态 0下架 1上架
     */
    private Integer skuStatus;

}
