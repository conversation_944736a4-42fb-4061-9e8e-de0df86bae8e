package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @date: 2022/10/14 下午5:51
 * @author: jashley
 */
@Data
@ApiModel
public class WarehouseOverseasConfigSaveCondition implements Serializable {
    private static final long serialVersionUID = 6925207631001192594L;


    private Integer id;

    @ApiModelProperty("销售国家")
    private String country;

    @ApiModelProperty("发货国家")
    private String deliverCountry;

    @ApiModelProperty("最小发货天数")
    private Integer minDeliveryPeriod;

    @ApiModelProperty("最大发货天数")
    private Integer maxDeliveryPeriod;

    @ApiModelProperty("退货仓库地址")
    private String refundAddress;

    @ApiModelProperty("退货国家")
    private String refundCountry;

    @ApiModelProperty("退货province")
    private String province;

    @ApiModelProperty("退货city")
    private String city;

    @ApiModelProperty("退货zipcode")
    private String zipcode;

    @ApiModelProperty("收件人")
    private String receiver;

    @ApiModelProperty("收件人联系号码")
    private String receiverPhone;

    @ApiModelProperty("可售状态 1可售0不可售")
    private Integer saleStatus;
}
