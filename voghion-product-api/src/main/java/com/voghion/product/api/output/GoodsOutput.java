package com.voghion.product.api.output;

import com.voghion.product.api.enums.OperationLogTypeEnums;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/17 15:20
 * @describe
 */
@Data
public class GoodsOutput  implements Serializable {

    private Long id;

    /**
     * 商品全局编号
     */
    private String itemNumber;

    /**
     * 商品名称
     */
    private String name;


    /**
     * 商品类型 1表示单品 2表示组合商品
     */
    private Integer type;


    /**
     * 商品编码
     */
    private String code;

    /**
     * 区域code,商品上架所属区域,暂时不做处理
     */
    private String areaCode;

    /**
     * 是否上架 0-下架  1-上架
     */
    private String isShow;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态 1表示正常 99表示禁用
     */
    private Integer status;

    /**
     * 是否删除 0表示未删除 1表示已删除
     */
    private Integer isDel;

    /**
     * 最低销售价格
     */
    private BigDecimal minPrice;

    /**
     * 最低展示价格
     */
    private BigDecimal minMarketPrice;

    /**
     * 最高销售价格
     */
    private BigDecimal maxPrice;

    /**
     * 最高展示价格
     */
    private BigDecimal maxMarketPrice;

    /**
     * 最低拼团价
     */
    private BigDecimal minGrouponPrice;

    /**
     * 最高拼团价
     */
    private BigDecimal maxGrouponPrice;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 商品原始名称
     */
    private String originalName;

    /**
     * 是否已翻译，0:未翻译，1:已翻译
     */
    private Integer translated;

    /**
     * 冗余类目Id,一个商品只对应一个后台类目Id
     */
    private Long categoryId;

    private String category;

    private String priceRange;

    private String grouponPriceRange;

    private String freightRange;

    /**
     * 类目路径
     */
    private String categoryString;

    /**
     * 冗余product_id
     */
    private Long productId;

    /**
     * 原始最低销售价格
     */
    private BigDecimal orginalMinPrice;

    /**
     * 原始最低展示价格
     */
    private BigDecimal orginalMinMarketPrice;

    /**
     * 原始最高销售价格
     */
    private BigDecimal orginalMaxPrice;

    /**
     * 原始最高展示价格
     */
    private BigDecimal orginalMaxMarketPrice;

    /**
     * 折扣标签
     */
    private String discountLabel;

    private BigDecimal discount;


    private Byte isFix;

    private String weight;

    /**
     * 销售数量
     */
    private Long sold;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商品原始id
     */
    private String itemCode;

    private String tag;

    private Integer isLock;

    private String SevenDayCountry;

    private String country;

    private Integer minStock;

    private Object goodsFreightList;

    /**
     * 尺码表数据
     */
    private String sizeChartData;

    private List<Long> tagIds;


    /**
     * extend 暂后台商品操作时传递
     */
    private OperationLogTypeEnums operationLogType;
    private String operator;

    private Integer logisticsProperty;
}
