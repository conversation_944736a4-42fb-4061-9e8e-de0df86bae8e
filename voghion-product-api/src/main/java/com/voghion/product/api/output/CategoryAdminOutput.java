package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/6/18 21:27
 * @describe
 */
@Data
public class CategoryAdminOutput implements Serializable {

    /**
     * 分类id
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 父类ID 0表示当前类目为1级类目
     */
    private Long parentId;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 后台类目树等级
     */
    private Integer levels;

    /**
     * 类目图片
     */
    private String imgUrl;

    private Boolean isLeaf;
}
