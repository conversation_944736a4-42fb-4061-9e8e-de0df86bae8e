package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "精品店铺")
public class QualityStoreInput implements Serializable {

    /**
     * id
     */
    private Long id;
    /**
     * 店铺名字
     */
    @ApiModelProperty(value = "店铺名字")
    private String shopName;
    /**
     * 商户id
     */
    @ApiModelProperty(value = "商品Id")
    private Long shopId;
    /**
     * 商品id字符串，用,隔开
     */
    @ApiModelProperty(value = "商品id字符串，用,隔开")
    private String goodsIds;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
