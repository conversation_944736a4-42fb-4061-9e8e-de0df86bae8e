package com.voghion.product.api.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2022/7/28 16:34
 * @describe
 */
@Data
public class GoodsMoveVO implements Serializable {

    private Long id;

    /**
     * 来源
     */
    private String source;

    /**
     * 商品链接
     */
    private String goodsUrl;

    /**
     * 商户id
     */
    private Integer shopId;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 状态0待开始;1已完成;2失败
     */
    private Integer status;

    /**
     * 同步次数 大于3次 不在同步
     */
    private Integer syncCounts;

    private Long goodsMoveId;
}
