package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.ShopGoodsDataVO;
import com.voghion.product.api.dto.UserStoreCountVO;
import com.voghion.product.api.dto.UserStoreVO;
import com.voghion.product.api.output.QueryGoodsOrderDTO;

public interface UserStoreRemoteService {

    Result<PageView<UserStoreVO>> queryPage(UserStoreVO userStoreVO);


    Result<Integer> addOrCancelUserStore(UserStoreVO userStoreVO);

    Result<UserStoreVO> queryByOption(Long userId, QueryGoodsOrderDTO queryGoodsOrderDTO);

    Result<UserStoreCountVO> queryShopFollowersCount(UserStoreVO storeVO);
}
