package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.QualityStoreDataVO;
import com.voghion.product.api.dto.ShopGoodsDataVO;
import com.voghion.product.api.dto.StoreImgsVO;
import com.voghion.product.api.dto.UserStoreVO;
import com.voghion.product.api.input.GoodsRecommendVO;
import com.voghion.product.api.output.*;

import java.util.List;

/**
 * author archer
 * createOn 2021/3/29 15:18
 */
public interface UserGoodsRemoteService {




    /**
     * 1.0.6 复制queryGoodsOnlyPageByCategoryIds 分页查询商品集合
     * @param categoryOrderDTO
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsByCategoryIds(QueryGoodsByCategorysDTO categoryOrderDTO);

    /**
     * 根据虚拟商品列表Id查询商品
     * @param queryGoodsOrderDTO
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsOnlyPageByCustomId(QueryGoodsOrderDTO queryGoodsOrderDTO);

    /**
     * 分页查询商品集合
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsOnlyPageByGoodsIds(QueryGoodsOrderDTO queryGoodsOrderDTO);

    /**
     * 分页查询商品集合--- 用于首页替换坑位
     * @param queryGoodsOrderDTO
     * @return
     */
    Result<PageView<GoodsOnlyDTO>>  queryPitGoodsOnlyPageByGoodsIds(QueryGoodsOrderDTO queryGoodsOrderDTO);



    /**
     * 分页查询商品集合---  用于分类页替换坑位商品
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsOnlyPitPageByCategoryIds(QueryGoodsCategoryOrderDTO categoryOrderDTO);


    /**
     * 分页查询商品集合
     * @param categoryOrderDTO
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsOnlyPageByCategoryIds(QueryGoodsCategoryOrderDTO categoryOrderDTO);

    /**
     * 根据搜索内容查询商品
     * @return
     */
    Result<PageView<GoodsOnlyDTO>>  queryGoodsOnlyPageBySearch(QueryGoodsBySearchDTO queryGoodsBySearchDTO);





    /**
     * 根据商品查询
     * @param goodIds
     * @return
     */
    Result<List<FrontCategoryDTO>> queryCategoryListByGoodIds(List<Long> goodIds);



    /***
     * 根据goodsOrderDTO 获取商品列表
     * @param goodsOrderDTO
     * @return
     */
    Result<PageView<GoodsOnlyDTO>> queryGoodsByShopName(QueryGoodsOrderDTO goodsOrderDTO);

    Result<PageView<QualityStoreDataVO>> queryQualityStoreData(Long userId,QueryGoodsOrderDTO goodsOrderDTO);


    Result<ShopGoodsDataVO> queryShopGoodsData(UserStoreVO vo);

    Result<ShopGoodsDataVO> queryShopGoodsDataNew(UserStoreVO vo);


    Result<StoreImgsVO> queryQualityStoreImg();

    Result<PageView<GoodsOnlyDTO>> queryRecommendGoodsByCategoryId( Long userId, GoodsRecommendVO goodsRecommendVO);

    /**
     * 用户流量访问时使用
     * @param goodsRecommendVO
     * @return
     */
    Result<PageView<Long>> queryFlowGoods(GoodsRecommendVO goodsRecommendVO);

    /**
     * 根就商品ids查询所有商品
     * @param queryGoodsOrderDTO
     * @return
     */
    Result<List<GoodsOnlyDTO>>  queryGoodsByIds(QueryGoodsOrderDTO queryGoodsOrderDTO);

    Result<PageView<GoodsOnlyDTO>> queryLoadPageGoodsInfo(GoodsRecommendVO goodsRecommendVO);


    }
