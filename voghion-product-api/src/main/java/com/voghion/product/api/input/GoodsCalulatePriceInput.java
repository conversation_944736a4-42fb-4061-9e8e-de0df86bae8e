package com.voghion.product.api.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodsCalulatePriceInput implements Serializable {

    private static final long serialVersionUID = 8937400883765295351L;

    /**
     * 商品ID
     */
    private Long goodsId;

    private Long categoryId;

    private Long shopId;

    /**
     * 商品属性列表
     */
    private List<GoodsItemDTO> goodsItemDTOS;

    private String weight;

    //查询类型 1-scm 2-全托管
    private Integer type;

    /**
     * 物流属性*
     */
    private Integer logisticsProperty;

}
