package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
@ApiModel
public class UpdateArrivalStatusVo implements Serializable {
    private static final long serialVersionUID = 5611925311048342285L;

    @ApiModelProperty("商品id集合")
    private List<Long> goodsIds;

    @ApiModelProperty("上新人")
    private String arrival;

    @ApiModelProperty("上新状态变更类型 0驳回 1已处理 2取消驳回")
    private Integer type;

    @ApiModelProperty("驳回原因")
    private String rejectReason;
}
