package com.voghion.product.api.input;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class GoodsSummary4ShopIndexDTO implements Serializable {

    private static final long serialVersionUID = -7879021324789539884L;
    /**
     * 禁售商品数量
     */
    private Integer countStopGoods;

    /**
     * 建议降价数量
     */
    private Integer countReducePrice;
}
