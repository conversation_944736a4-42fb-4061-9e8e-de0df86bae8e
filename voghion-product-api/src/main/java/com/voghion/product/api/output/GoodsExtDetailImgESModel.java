//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output;

import java.io.Serializable;
import java.time.LocalDate;

public class GoodsExtDetailImgESModel implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long goodsId;
    private String imgUrl;
    private LocalDate createTime;
    private String originalImgUrl;
    private Integer transported;

    public GoodsExtDetailImgESModel() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsId() {
        return this.goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getImgUrl() {
        return this.imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public LocalDate getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(LocalDate createTime) {
        this.createTime = createTime;
    }

    public String getOriginalImgUrl() {
        return this.originalImgUrl;
    }

    public void setOriginalImgUrl(String originalImgUrl) {
        this.originalImgUrl = originalImgUrl;
    }

    public Integer getTransported() {
        return this.transported;
    }

    public void setTransported(Integer transported) {
        this.transported = transported;
    }

    public String toString() {
        return "GoodsExtDetailImg{id=" + this.id + ", goodsId=" + this.goodsId + ", imgUrl=" + this.imgUrl + ", createTime=" + this.createTime + ", originalImgUrl=" + this.originalImgUrl + ", transported=" + this.transported + "}";
    }
}
