//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output.es;

import com.colorlight.base.utils.BeanCopy;
import com.colorlight.base.utils.CollectionUtil;
import com.voghion.product.api.output.GoodsEsVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class GoodsEsGoodsOperateLogVo implements Serializable {
    private static final long serialVersionUID = 3412593415896617136L;
    /**
     * 商品id
     */
    private String goodsId;
    /**
     * 操作类型 0新增 1修改 2删除
     */
    private Integer opt;


    private UpdateInfo newGoods;
    private UpdateInfo oldGoods;

    public GoodsEsGoodsOperateLogVo(GoodsEsGoodsVo vo) {
        if (vo == null) {
            return;
        }
        this.goodsId = vo.getGoodsId();
        this.opt = vo.getOpt();

        switch (vo.getOpt()) {
            case 0:
            case 1:
                this.newGoods = new UpdateInfo(vo);
                break;
            case 2:
                this.oldGoods = new UpdateInfo(vo);
                break;
            default:
                break;
        }
    }

    public GoodsEsGoodsOperateLogVo(GoodsEsGoodsVo oldGoods, GoodsEsGoodsVo newGoods) {
        if (null == oldGoods || null == newGoods) {
            return;
        }
        this.goodsId = newGoods.getGoodsId();
        this.opt = newGoods.getOpt();
        this.oldGoods = new UpdateInfo(oldGoods);
        this.newGoods = new UpdateInfo(newGoods);
    }


    @Data
    public static class UpdateInfo implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        /**
         * 上下架
         */
        private Integer isShow;
        /**
         * 是否删除
         */
        private Integer isDel;
        /**
         * 修改时间
         */
        private String updateTime;

        public UpdateInfo(GoodsEsGoodsVo vo) {
            this.isDel = vo.getIsDel();
            this.isShow = vo.getIsShow();
            this.updateTime = vo.getUpdateTime();
        }

//        public UpdateInfo() {
//        }
//
//        public UpdateInfo(GoodsEsVo.GoodsOpt ex) {
//            BeanCopy.copyObjValue(ex, this);
//        }
    }


}
