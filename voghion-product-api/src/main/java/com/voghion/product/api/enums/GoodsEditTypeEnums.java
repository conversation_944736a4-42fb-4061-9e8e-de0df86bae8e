package com.voghion.product.api.enums;

import com.voghion.product.utils.MathUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum GoodsEditTypeEnums {
    ON_SHELF(1, "上架"),
    OFF_SHELF(2, "下架"),
    PRICE(4, "价格"),
    STOCK(8, "库存"),
    WEIGHT(16, "重量"),
    PROPERTY(32, "规格"),
    DETAIL(64, "商详"),
    SKU_STATUS(128, "sku上下架"),

    REAL_SHOT_IMG(256, "实拍图"),
    ;


    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    GoodsEditTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getMsgByCode(Integer code) {
        for (GoodsEditTypeEnums enums : GoodsEditTypeEnums.values()) {
            if (enums.code.equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }


    public static String getMsg(Integer code) {
        List<Integer> codes = MathUtils.splitBinary(code);
        return codes.stream()
                .map(GoodsEditTypeEnums::getMsgByCode)
                .collect(Collectors.joining("、"));
    }

    public static List<Integer> listAllTypeCodes() {
        return Arrays.stream(GoodsEditTypeEnums.values()).map(GoodsEditTypeEnums::getCode).collect(Collectors.toList());
    }

    public static String listAllTypeNameByCodes(Set<Integer> types) {
        if (CollectionUtils.isEmpty(types)) {
            return "";
        }
        return types.stream().map(type -> {
            for (GoodsEditTypeEnums value : GoodsEditTypeEnums.values()) {
                if (value.getCode().equals(type)) {
                    return value.getDesc();
                }

            }
            return "None";
        }).collect(Collectors.joining("、"));
    }
}
