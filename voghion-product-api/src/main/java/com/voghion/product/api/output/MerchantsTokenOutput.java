package com.voghion.product.api.output;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
public class MerchantsTokenOutput implements Serializable {
    private static final long serialVersionUID = 7359155360232245935L;

    /**
     * id
     */

    private Integer id;

    /**
     * 商户id
     */
    private Integer shopId;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * token所用名称
     */
    private String title;

    /**
     * 商户token
     */
    private String token;

    /**
     * 备注
     */
    private String desc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 账号id(冗余字段)
     */
    private Integer userId;

    /**
     * 0停用1生效
     */
    private Integer status;

    /**
     *  发货类型 0.代发商家 1.直发商家 2.香港代发 3.自营店铺 4.scm自营店铺
     */
    private Integer deliveryType;

    /**
     * 店铺销售状态 1正常 2休假 3禁售
     */
    private Integer isSale;
}
