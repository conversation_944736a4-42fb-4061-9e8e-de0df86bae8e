package com.voghion.product.api.input;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品查询对象
 * <AUTHOR>
 */
public class GoodsEsQueryInput implements Serializable {

    private static final long serialVersionUID = 8937400883765295351L;
    /**
     * 商品名称 
     */
    private String goodsName;

    /**
     * 排序
     */
    private Integer orderBy;

    /**
     * 商品名称
     */
    private String name;


    /**
     * 属性名, [属性值list] map
     */
    private Map<String, List<String>> propertyInfos; 
    
    /**
     * 最低价
     */
    private BigDecimal minPrice;
    
    /**
     * 最高价
     */
    private BigDecimal maxPrice;
    
    /**
     * 排序规则 1-按时间倒序排序 2-按时间顺序排序 3-按销量倒序排序 
     *                  4-按销量顺序排序 5-按价格倒序排序 6-按价格顺序排序
     */
    private Byte orderType;
    
    /**
     * 类目ID
     */
    private Long categoryId;
    
    /**
     * 商品ID列表
     */
    private List<Long> goodsIds;
    
    /**
     * 每页显示几条记录
     */
    private Integer pageSize;

    /**
     * 标签名称
     */
    private List<String> tagNames;

    /**
     * 默认 当前页 为第一页 这个数是计算出来的
     */
    private Integer pageNow;

    /**
     * 国家
     */
    private String country;

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Map<String, List<String>> getPropertyInfos() {
        return propertyInfos;
    }

    public void setPropertyInfos(Map<String, List<String>> propertyInfos) {
        this.propertyInfos = propertyInfos;
    }
    
    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategorys(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }
    
    public List<Long> getGoodsIds() {
        return goodsIds;
    }
    
    public void setGoodsIds(List<Long> goodsIds) {
        this.goodsIds = goodsIds;
    }
    
    public Integer getPageSize() {
        return pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public Integer getPageNow() {
        return pageNow;
    }
    
    public void setPageNow(Integer pageNow) {
        this.pageNow = pageNow;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public List<String> getTagNames() {
        return tagNames;
    }

    public void setTagNames(List<String> tagNames) {
        this.tagNames = tagNames;
    }
}
