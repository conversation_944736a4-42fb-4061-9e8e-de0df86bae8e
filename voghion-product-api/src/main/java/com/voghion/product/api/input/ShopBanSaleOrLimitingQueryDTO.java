package com.voghion.product.api.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ShopBanSaleOrLimitingQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 6046400396011090809L;
    /**
     * 店铺ID，多个用换行分隔
     */
    @ApiModelProperty("店铺ID，多个用换行分隔")
    private String shopIds;

    /**
     * 状态 1:已解禁售 99:禁售中 10:已解限流 20:限流中
     */
    @ApiModelProperty("状态 1:已解禁售 99:禁售中 10:已解限流 20:限流中")
    private Integer status;

    /**
     * 店铺名称
     */
    @ApiModelProperty("店铺名称")
    private String shopName;

    /**
     * 封禁/限流原因
     */
    @ApiModelProperty("封禁/限流原因")
    private String reason;

    /**
     * 创建开始时间
     */
    @ApiModelProperty("创建开始时间")
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    @ApiModelProperty("创建结束时间")
    private Date createEndTime;

    /**
     * 创建开始时间
     */
    @ApiModelProperty("更新开始时间")
    private Date updateStartTime;

    /**
     * 创建结束时间
     */
    @ApiModelProperty("更新结束时间")
    private Date updateEndTime;
}
