package com.voghion.product.api.service;

import com.voghion.product.api.dto.GoodsLockInfoDto;
import com.voghion.product.model.vo.condition.UpdateLockLabelCondition;

import java.util.List;

public interface GoodsLockRemoteService {
    /**
     * 新增锁定标签
     */
    boolean addLockByApi(UpdateLockLabelCondition condition);

    /**
     * 移除锁定标签
     */
    boolean removeLockByApi(UpdateLockLabelCondition condition);

    /**
     * 根据商品ids查询商品锁定标签
     */
    List<GoodsLockInfoDto> listGoodsLockInfoByGoodsIds(List<Long> goodsIds);
}
