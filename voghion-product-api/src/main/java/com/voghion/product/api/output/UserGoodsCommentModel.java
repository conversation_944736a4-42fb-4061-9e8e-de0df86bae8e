package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户评论DTO
 *
 * <AUTHOR> chen<PERSON>uan
 * @date 2021/04/09 14:09
 **/
@Data
public class UserGoodsCommentModel implements Serializable {


    private Long id;

    /**
     * 评论标签列表
     */
    private List<String> labels;

    /**
     * 用户userId
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String headImg;


    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品权重
     */
    private BigDecimal sortValue;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * skuId信息
     */
    private Long skuId;

    /**
     * SKU信息
     */
    private String skuName;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 评星 1:1星;2:2星;......5:5星
     */
    private Double score;

    /**
     * 评论图片 逗号隔开
     */
    private List<String> imgUrl;

    private String videoUrl;


    /**
     * 订单id
     */
    private String orderId;

    /**
     * 状态:10:待审核;20:已发布;30:举报中;40:已屏蔽
     */
    private Integer status;

    /**
     * 默认 0
     */
    private Integer sort;


    /**
     * 评论时间
     */
    private String createTime;
}
