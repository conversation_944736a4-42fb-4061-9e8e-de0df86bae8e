package com.voghion.product.api.input;

import com.colorlight.base.model.PageView;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * author archer
 * createOn 2021/4/27 16:24
 */
@Data
public class QueryGoodsDTO extends PageView {

    /**
     * 商品Id
     */
    private Long goodsId;

    /**
     * 商品Ids
     */
    private List<Long> goodsIds;

    /**
     * 是否上下架
     */
    private String isShow;

    /**
     * 商户店铺id
     */
    private Long shopId;

    /**
     * 类目id集合
     */
    private List<Long> categoryIds;

    private boolean querySelfSupportShopGoods = false;

    /**
     * 开始时间(更新时间)"
     */
    private Date startTime;

    /**
     * 结束时间(更新时间)
     */
    private Date endTime;
}
