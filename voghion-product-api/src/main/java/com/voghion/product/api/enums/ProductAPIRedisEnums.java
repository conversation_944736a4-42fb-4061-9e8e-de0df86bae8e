package com.voghion.product.api.enums;

public enum ProductAPIRedisEnums {

	FRONT_CATEGORY_KEY("NEW_FRONT_CATEGORY_","前台类目的对象缓存"),
	CATEGORY_KEY("Category:","后台类目的对象缓存"),
	CATE_BY_FRONT_CATEGORY_KEY("CategoryByFront:","前台类目下后台类目的对象缓存"),
	CHILD_CATEGORY_ID_BY_CATE("ChildCateIDByCate:",""),
	ALL_CHILD_CATEGORY_ID_BY_CATE("AllChildCateIDByCate:","当前类目下的所有子类目(叶子+非叶子+自身)"),
	FRONT_CATEGORY_ID_MAPPING_CATEGORY_ID_LIST("FRONT_CATEGORY_ID_MAPPING_CATEGORY_ID_LIST_V2:","前端类目ID映射后端类目ID集合"),
	;

	private String code;
	private String desc;

	private ProductAPIRedisEnums(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
	
	
	

}
