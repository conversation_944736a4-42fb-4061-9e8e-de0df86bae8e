//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.voghion.product.api.output.es;

import com.colorlight.base.utils.BeanCopy;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class GoodsEsFreightOperateLogVo implements Serializable {
    private Long goodsId;
    private Long skuId;

    private UpdateInfo oldFreightInfo;
    private UpdateInfo newFreightInfo;

    public GoodsEsFreightOperateLogVo(GoodsEsFreightVo ex) {
        this.goodsId = ex.getGoodsId();
        this.skuId = ex.getSkuId();
        this.newFreightInfo = new UpdateInfo(ex);
    }

    public GoodsEsFreightOperateLogVo(GoodsEsFreightVo oldEx, GoodsEsFreightVo newEx) {
        this.goodsId = oldEx.getGoodsId();
        this.skuId = newEx.getSkuId();
        this.oldFreightInfo = new UpdateInfo(oldEx);
        this.newFreightInfo = new UpdateInfo(newEx);
    }


    @Data
    public static class UpdateInfo implements Serializable {
        private static final long serialVersionUID = -97130443170094069L;
        private Long opt;
        private String code;
        /**
         * 默认运费
         */
        private BigDecimal currentFreight;
        /**
         * 价格
         */
        private BigDecimal price;
        private String createTime;
        private String updateTime;


        public UpdateInfo(GoodsEsFreightVo ex) {
            BeanCopy.copyObjValue(ex, this);
        }
    }
}
