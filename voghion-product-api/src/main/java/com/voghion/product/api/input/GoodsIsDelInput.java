package com.voghion.product.api.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "商品删除入参")
public class GoodsIsDelInput implements Serializable {

    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Ids")
    private List<Long> goodsIds;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    private String operator;

}
