package com.voghion.product.api.output;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsESModelSimpleVo implements Serializable {
    private static final long serialVersionUID = 2392826608634860102L;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 最低销售价格
     */
    private BigDecimal minPrice;

    /**
     * 最低展示价格
     */
    private BigDecimal minMarketPrice;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 销售量
     */
    private Long sales;

    /**
     * 商品评论平均分
     */
    private Double commentAverage;

    /**
     * 货币符号
     */
    private String currencyMark;

    /**
     * 货币展示规则
     */
    private String priceRuler;

}
