package com.voghion.product.api.input;

import com.colorlight.base.model.PageView;
import com.voghion.product.api.output.CategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/28 15:55
 * @describe
 */

@Data
@ApiModel
public class CategoryCountryInputVO extends PageView {



    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;


    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    /**
     * 国家code
     */
    @ApiModelProperty(value = "国家code")
    private String countryCode;


    /**
     * 0 所有平台  1 android  2 ios   3 pc，4 h5
     */
    @ApiModelProperty(value = "0 所有平台  1 android  2 ios   3 pc，4 h5")
    private Integer platform;


    @ApiModelProperty(value = "类目集合")
    private List<CategoryVO>  categoryList;


    @ApiModelProperty(value = "批量删除的国家code")
    private List<String> countryCodes;

    @ApiModelProperty(value = "批量删除ids")
    private List<Long> ids;
}
