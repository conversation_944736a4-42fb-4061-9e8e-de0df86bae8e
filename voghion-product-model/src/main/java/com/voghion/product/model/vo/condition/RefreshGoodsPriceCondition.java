package com.voghion.product.model.vo.condition;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RefreshGoodsPriceCondition implements Serializable {
    private static final long serialVersionUID = 7526807770623193585L;

    private List<Long> shopIds;

    private Integer isShow;

    /**
     * 系数
     */
    private BigDecimal rate;
}
